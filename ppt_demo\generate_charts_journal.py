#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成顶刊风格的图表 - 用于原尺寸插入PPT
纵标题10pt，其他文字8pt，符合顶级期刊发表标准
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import matplotlib.patches as patches
import cv2
from PIL import Image

class JournalStyleChartGenerator:
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.reason_path = self.base_path / "reason"
        self.output_path = Path("E:/reasoning/ppt_demo")
        self.output_path.mkdir(exist_ok=True)
        
        # 图表规格 - 根据PPT宽度17cm计算
        # PPT宽度17cm = 481.89点，留边距后可用宽度约450点
        # IEEE图表占60%，像素分布图占35%，间距5%
        self.ieee_width_points = 270  # 约60%
        self.pixel_width_points = 175  # 约35%
        self.chart_height_points = 120  # 统一高度
        
        # 转换为英寸 (72点 = 1英寸)
        self.ieee_width = self.ieee_width_points / 72
        self.pixel_width = self.pixel_width_points / 72
        self.chart_height = self.chart_height_points / 72
        
        self.dpi = 300  # 高质量输出
        
        # 顶刊风格字体设置
        plt.rcParams.update({
            'font.family': 'Arial',
            'font.size': 8,                    # 默认8pt
            'axes.labelsize': 10,              # 纵标题10pt
            'axes.titlesize': 10,              # 子图标题10pt
            'xtick.labelsize': 8,              # X轴刻度8pt
            'ytick.labelsize': 8,              # Y轴刻度8pt
            'legend.fontsize': 8,              # 图例8pt
            'figure.titlesize': 12,            # 总标题12pt（如果有）
            'axes.linewidth': 0.8,             # 轴线粗细
            'grid.linewidth': 0.5,             # 网格线粗细
            'lines.linewidth': 1.5,            # 线条粗细
            'patch.linewidth': 0.8,            # 箱线图边框
            'xtick.major.width': 0.8,          # 刻度线粗细
            'ytick.major.width': 0.8,
            'xtick.minor.width': 0.6,
            'ytick.minor.width': 0.6,
            'axes.spines.left': True,          # 显示轴线
            'axes.spines.bottom': True,
            'axes.spines.top': False,          # 隐藏顶部和右侧轴线
            'axes.spines.right': False,
            'axes.grid': True,                 # 默认显示网格
            'grid.alpha': 0.3,                # 网格透明度
            'figure.facecolor': 'white',       # 背景色
            'axes.facecolor': 'white'
        })
        
        # 模型配置
        self.models = ["Cycle_GAN", "unet", "FADformer", "MoCE-IR", "Moce-GAN"]
        self.model_display_names = {
            "Cycle_GAN": "CycleGAN",
            "unet": "U-Net", 
            "FADformer": "FADformer",
            "MoCE-IR": "MoCE-IR",
            "Moce-GAN": "MoCE-GAN"
        }
        
        # 像素分布图需要包含GT
        self.pixel_models = ["GT"] + list(self.models)
        self.pixel_display_names = {
            "GT": "GT",
            **self.model_display_names
        }
        
        # 顶刊风格配色方案 - 使用更专业的颜色
        self.colors = [
            '#1f77b4',  # 蓝色
            '#ff7f0e',  # 橙色
            '#2ca02c',  # 绿色
            '#d62728',  # 红色
            '#9467bd'   # 紫色
        ]
        self.pixel_colors = ['#000000'] + self.colors  # GT用黑色
    
    def load_data(self, dataset_type: str):
        """加载数据"""
        all_data = []
        
        for model in self.models:
            # 查找模型目录下的CSV文件
            model_dir = self.reason_path / model
            if model_dir.exists():
                # 查找匹配的子目录
                pattern = f"{dataset_type}_data16_512_inference_*"
                matching_dirs = list(model_dir.glob(pattern))
                
                if matching_dirs:
                    # 使用第一个匹配的目录
                    inference_dir = matching_dirs[0]
                    csv_files = list(inference_dir.glob("inference_logs/*.csv"))
                    
                    if csv_files:
                        csv_path = csv_files[0]  # 使用第一个CSV文件
                        df = pd.read_csv(csv_path)
                        df['Model'] = self.model_display_names[model]
                        all_data.append(df)
                        print(f"✅ 加载 {model}: {len(df)} 条记录")
                    else:
                        print(f"⚠️ 未找到CSV文件: {inference_dir}")
                else:
                    print(f"⚠️ 未找到匹配目录: {model_dir}/{pattern}")
            else:
                print(f"⚠️ 模型目录不存在: {model_dir}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"📊 总计加载: {len(combined_df)} 条记录")
            return combined_df
        return None
    
    def generate_ieee_chart_journal(self, dataset_type: str = "peitai"):
        """生成顶刊风格的IEEE图表"""
        print(f"📊 生成顶刊风格IEEE图表 - {dataset_type}数据集")
        
        df = self.load_data(dataset_type)
        if df is None:
            return False
        
        # 创建图表
        fig, axes = plt.subplots(1, 3, figsize=(self.ieee_width, self.chart_height))
        
        metrics = ['PSNR', 'SSIM', 'LPIPS']
        metric_labels = ['PSNR (dB)', 'SSIM', 'LPIPS']
        
        for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
            ax = axes[i]
            
            # 创建箱线图
            box_data = [df[df['Model'] == model][metric].values 
                       for model in [self.model_display_names[m] for m in self.models]]
            
            bp = ax.boxplot(box_data, tick_labels=[self.model_display_names[m] for m in self.models],
                           patch_artist=True, showfliers=False,
                           boxprops=dict(linewidth=0.8),
                           whiskerprops=dict(linewidth=0.8),
                           capprops=dict(linewidth=0.8),
                           medianprops=dict(linewidth=1.0, color='black'))
            
            # 设置颜色
            for patch, color in zip(bp['boxes'], self.colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            # 设置标签和样式
            ax.set_ylabel(label, fontsize=10, fontweight='bold')  # 纵标题10pt

            # 设置X轴标签，确保对齐
            ax.set_xticks(range(len(self.models)))  # 确保刻度位置正确
            ax.set_xticklabels([self.model_display_names[m] for m in self.models],
                              rotation=60, fontsize=7, ha='right')  # 右对齐，避免重合
            ax.tick_params(axis='y', labelsize=8)                 # Y轴标签8pt
            
            # 设置Y轴范围
            if metric == 'PSNR':
                ax.set_ylim(20, 40)
            elif metric == 'SSIM':
                ax.set_ylim(0.6, 1.0)
            elif metric == 'LPIPS':
                ax.set_ylim(0, 0.4)
            
            # 美化网格
            ax.grid(True, alpha=0.3, linewidth=0.5)
            ax.set_axisbelow(True)
        
        # 调整子图间距，增加间距避免重合
        plt.tight_layout(pad=1.0)
        plt.subplots_adjust(wspace=0.6, bottom=0.25)  # 增加子图间距，为X轴标签留更多空间
        
        # 保存
        output_file = self.output_path / f"ieee_chart_journal_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', pad_inches=0.05)
        plt.close()
        
        print(f"✅ 顶刊风格IEEE图表已保存: {output_file}")
        return True
    
    def extract_pixel_line(self, image_path: Path, direction='vertical'):
        """从图像中提取垂直或水平线的像素强度"""
        try:
            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"⚠️ 无法读取图像: {image_path}")
                return None
            
            # 转换为灰度
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape
            
            if direction == 'vertical':
                # 提取中心垂直线
                center_x = w // 2
                line_pixels = gray[:, center_x]
            else:  # horizontal
                # 提取中心水平线
                center_y = h // 2
                line_pixels = gray[center_y, :]
            
            # 归一化到0-1
            normalized = line_pixels.astype(np.float32) / 255.0
            return normalized
            
        except Exception as e:
            print(f"❌ 提取像素线失败 {image_path}: {e}")
            return None
    
    def load_pixel_data(self, dataset_type: str):
        """加载真实的像素分布数据"""
        print(f"📊 加载真实像素数据 - {dataset_type}数据集")
        
        # 数据路径
        filter_path = self.base_path / "visual" / "filter"
        
        pixel_data = {}
        
        # GT文件路径
        gt_files = {
            'peitai': {
                'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_GT_region1_enlarged.png",
            }
        }
        
        # 模型文件映射
        model_files = {
            'peitai': {
                'Cycle_GAN': {
                    'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_Restored_region1_enlarged.png",
                },
                'unet': {
                    'region1': filter_path / "unet" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR28.28_Restored_region1_enlarged.png",
                },
                'FADformer': {
                    'region1': filter_path / "FADformer" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR31.12_Restored_region1_enlarged.png",
                },
                'MoCE-IR': {
                    'region1': filter_path / "MoCE-IR" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.88_Restored_region1_enlarged.png",
                },
                'Moce-GAN': {
                    'region1': filter_path / "Moce-GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR38.30_Restored_region1_enlarged.png",
                }
            }
        }
        
        # 提取GT像素数据
        gt_region1_v = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'vertical')
        gt_region1_h = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'horizontal')
        
        if gt_region1_v is not None and gt_region1_h is not None:
            pixel_data['GT'] = {
                'vertical': gt_region1_v,
                'horizontal': gt_region1_h
            }
            print(f"✅ 加载GT像素数据: {len(gt_region1_v)} 像素点")
        
        # 提取各模型像素数据
        for model in self.models:
            if dataset_type in model_files and model in model_files[dataset_type]:
                region1_path = model_files[dataset_type][model]['region1']
                
                if region1_path.exists():
                    v_pixels = self.extract_pixel_line(region1_path, 'vertical')
                    h_pixels = self.extract_pixel_line(region1_path, 'horizontal')
                    
                    if v_pixels is not None and h_pixels is not None:
                        pixel_data[model] = {
                            'vertical': v_pixels,
                            'horizontal': h_pixels
                        }
                        print(f"✅ 加载{self.model_display_names[model]}像素数据: {len(v_pixels)} 像素点")
                else:
                    print(f"⚠️ 文件不存在: {region1_path}")
        
        return pixel_data
    
    def generate_pixel_distribution_journal(self, dataset_type: str = "peitai"):
        """生成顶刊风格的像素分布图"""
        print(f"📈 生成顶刊风格像素分布图 - {dataset_type}数据集")

        # 加载真实像素数据
        pixel_data = self.load_pixel_data(dataset_type)

        if not pixel_data:
            print("❌ 无法加载像素数据")
            return False

        # 创建图表，增加顶部空间用于图例
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.pixel_width, self.chart_height))

        # 绘制垂直线分析
        color_idx = 0
        legend_handles = []
        legend_labels = []

        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                display_name = self.pixel_display_names.get(model_key, model_key)
                v_data = pixel_data[model_key]['vertical']

                x_positions = np.arange(len(v_data))
                linewidth = 2.0 if model_key == 'GT' else 1.5

                line, = ax1.plot(x_positions, v_data,
                               color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)

                # 收集图例信息（只收集一次）
                legend_handles.append(line)
                legend_labels.append(display_name)
                color_idx += 1

        ax1.set_ylabel('Pixel Value', fontsize=10, fontweight='bold')  # 纵标题10pt
        # 删除X轴标签，避免重复
        ax1.tick_params(axis='both', labelsize=8)                     # 刻度8pt
        ax1.set_ylim(0, 1)
        ax1.grid(True, alpha=0.3, linewidth=0.5)
        ax1.set_axisbelow(True)

        # 绘制水平线分析（不添加图例）
        color_idx = 0
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                h_data = pixel_data[model_key]['horizontal']

                x_positions = np.arange(len(h_data))
                linewidth = 2.0 if model_key == 'GT' else 1.5

                ax2.plot(x_positions, h_data,
                        color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)
                color_idx += 1

        ax2.set_ylabel('Pixel Value', fontsize=10, fontweight='bold')  # 纵标题10pt
        ax2.set_xlabel('Position (pixels)', fontsize=8)               # 保留底部X轴标签
        ax2.tick_params(axis='both', labelsize=8)                     # 刻度8pt
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3, linewidth=0.5)
        ax2.set_axisbelow(True)

        # 在图表顶部添加统一图例，2×3排版
        fig.legend(legend_handles, legend_labels,
                  loc='upper center',
                  bbox_to_anchor=(0.5, 0.96),  # 位置稍微下移避免重合
                  ncol=3,  # 3列排版
                  fontsize=6,  # 改为6pt字体
                  frameon=True,
                  fancybox=True,
                  shadow=True,
                  columnspacing=1.2,  # 增加列间距避免重合
                  handlelength=1.8,   # 稍微缩短图例线长度
                  handletextpad=0.5)  # 减少图例线和文字间距

        # 调整布局，增加子图间距，为顶部图例留空间
        plt.tight_layout(pad=0.6)
        plt.subplots_adjust(hspace=0.5, top=0.82)  # 优化间距，为6pt图例留合适空间

        # 保存
        output_file = self.output_path / f"pixel_distribution_journal_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none', pad_inches=0.05)
        plt.close()

        print(f"✅ 顶刊风格像素分布图已保存: {output_file}")
        return True
    
    def generate_all_charts(self, dataset_type: str = "peitai"):
        """生成所有顶刊风格图表"""
        print("🎨 生成顶刊风格的科研图表")
        print("=" * 50)
        print(f"📊 配置信息:")
        print(f"   - 数据集: {dataset_type}")
        print(f"   - IEEE图表尺寸: {self.ieee_width:.2f}\" × {self.chart_height:.2f}\" ({self.ieee_width_points}×{self.chart_height_points}点)")
        print(f"   - 像素分布图尺寸: {self.pixel_width:.2f}\" × {self.chart_height:.2f}\" ({self.pixel_width_points}×{self.chart_height_points}点)")
        print(f"   - 纵标题字体: 10pt (加粗)")
        print(f"   - 其他文字: 8pt")
        print(f"   - 输出DPI: {self.dpi}")
        print("=" * 50)
        
        success1 = self.generate_ieee_chart_journal(dataset_type)
        success2 = self.generate_pixel_distribution_journal(dataset_type)
        
        if success1 and success2:
            print("\n🎉 所有顶刊风格图表生成完成！")
            print(f"📁 输出目录: {self.output_path}")
            return True
        else:
            print("\n❌ 图表生成失败！")
            return False

def main():
    """主函数"""
    generator = JournalStyleChartGenerator()
    generator.generate_all_charts("peitai")

if __name__ == "__main__":
    main()
