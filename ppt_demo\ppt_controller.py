#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT控制器 - 专门操作 pingtu.pptx
使用COM接口实时修改PowerPoint内容
"""

import win32com.client
from pathlib import Path
import time

class PingtuPPTController:
    """专门控制pingtu.pptx的类"""
    
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.ppt_path = self.base_path / "ppt_demo" / "pingtu.pptx"
        self.image_path = self.base_path / "ppt_demo"  # 使用ppt_demo目录下的图片

        self.ppt_app = None
        self.presentation = None

        # 统一规格的图片路径
        self.ieee_image = self.image_path / "ieee_chart_peitai.png"
        self.pixel_image = self.image_path / "pixel_distribution_peitai.png"
    
    def connect(self):
        """连接到PowerPoint并找到pingtu.pptx"""
        try:
            print("🔗 连接到PowerPoint...")
            self.ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            
            # 查找pingtu.pptx
            target_name = "pingtu.pptx"
            for i in range(1, self.ppt_app.Presentations.Count + 1):
                pres = self.ppt_app.Presentations(i)
                if Path(pres.Name).name == target_name:
                    self.presentation = pres
                    print(f"✅ 找到 {target_name}")
                    return True
            
            # 如果没找到，尝试打开
            if self.ppt_path.exists():
                print(f"📂 打开 {target_name}...")
                self.presentation = self.ppt_app.Presentations.Open(str(self.ppt_path))
                print(f"✅ 已打开 {target_name}")
                return True
            else:
                print(f"❌ 文件不存在: {self.ppt_path}")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_slide(self, slide_num=1):
        """获取指定幻灯片"""
        if not self.presentation:
            return None
        return self.presentation.Slides(slide_num)
    
    def clear_slide(self, slide_num=1):
        """清空指定幻灯片的所有内容"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False
            
            print(f"🧹 清空幻灯片 {slide_num}...")
            
            # 从后往前删除所有形状
            for i in range(slide.Shapes.Count, 0, -1):
                slide.Shapes(i).Delete()
            
            print(f"✅ 幻灯片 {slide_num} 已清空")
            return True
            
        except Exception as e:
            print(f"❌ 清空失败: {e}")
            return False
    
    def add_research_charts(self, slide_num=1):
        """添加科研规格的图表到幻灯片底部"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False

            print("📊 添加科研规格图表...")

            # PPT尺寸
            slide_width = self.presentation.PageSetup.SlideWidth
            slide_height = self.presentation.PageSetup.SlideHeight

            # 计算位置（底部并排，IEEE图表更宽）
            margin = 15
            total_width = slide_width - 2 * margin
            gap = 10  # 图表间距

            # IEEE图表占60%宽度，像素分布图占35%宽度
            ieee_width = total_width * 0.6
            pixel_width = total_width * 0.35

            # 统一高度
            chart_height = 120  # 统一高度

            # 位置计算
            ieee_left = margin
            ieee_top = slide_height - chart_height - margin

            pixel_left = margin + ieee_width + gap
            pixel_top = slide_height - chart_height - margin

            # 添加IEEE图表（宽版）
            if self.ieee_image.exists():
                slide.Shapes.AddPicture(
                    FileName=str(self.ieee_image),
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=ieee_left,
                    Top=ieee_top,
                    Width=ieee_width,
                    Height=chart_height
                )
                print(f"✅ IEEE图表已添加 (宽度: {ieee_width:.0f}点)")
            else:
                print(f"❌ IEEE图表文件不存在: {self.ieee_image}")
                return False

            # 添加像素分布图（窄版）
            if self.pixel_image.exists():
                slide.Shapes.AddPicture(
                    FileName=str(self.pixel_image),
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=pixel_left,
                    Top=pixel_top,
                    Width=pixel_width,
                    Height=chart_height
                )
                print(f"✅ 像素分布图已添加 (宽度: {pixel_width:.0f}点)")
            else:
                print(f"❌ 像素分布图文件不存在: {self.pixel_image}")
                return False

            print(f"📏 图表规格: IEEE({ieee_width:.0f}×{chart_height}) + 像素({pixel_width:.0f}×{chart_height})")
            return True

        except Exception as e:
            print(f"❌ 添加图表失败: {e}")
            return False
    

    
    def get_info(self):
        """获取PPT信息"""
        if not self.presentation:
            print("❌ 未连接到演示文稿")
            return
        
        print("📊 PPT信息:")
        print(f"   - 文件名: {self.presentation.Name}")
        print(f"   - 幻灯片数量: {self.presentation.Slides.Count}")
        print(f"   - 尺寸: {self.presentation.PageSetup.SlideWidth:.0f} x {self.presentation.PageSetup.SlideHeight:.0f} 点")
        
        if self.presentation.Slides.Count > 0:
            slide = self.presentation.Slides(1)
            print(f"   - 第1张幻灯片形状数: {slide.Shapes.Count}")
    
    def save(self):
        """保存PPT"""
        try:
            if self.presentation:
                self.presentation.Save()
                print("💾 PPT已保存")
                return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
        return False

def main():
    """主函数 - 只插入科研规格图表"""
    print("🎯 科研图表PPT控制器")
    print("=" * 40)

    controller = PingtuPPTController()

    # 连接
    if not controller.connect():
        return

    # 显示信息
    controller.get_info()

    print("\n🎨 开始操作...")

    # 清空幻灯片
    controller.clear_slide()

    # 添加科研规格图表
    controller.add_research_charts()

    # 保存
    controller.save()

    print("\n✅ 科研图表插入完成！请查看PowerPoint中的变化")
    print("📊 图表特点:")
    print("   - IEEE图表：更宽，适合展示多指标对比")
    print("   - 像素分布图：较窄，适合展示分布分析")
    print("   - 统一高度和字体大小，符合科研标准")

if __name__ == "__main__":
    main()
