#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将IEEE图表和像素分布图拆分为5个独立图片
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 8

class IndividualChartGenerator:
    """独立图表生成器"""
    
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.reason_path = self.base_path / "reason"  # 模型推理结果路径
        self.output_path = self.base_path / "ppt_demo"
        
        # 确保输出目录存在
        self.output_path.mkdir(exist_ok=True)
        
        # 模型配置
        self.models = ['Cycle_GAN', 'unet', 'FADformer', 'MoCE-IR', 'Moce-GAN']
        self.model_display_names = {
            'Cycle_GAN': 'CycleGAN',
            'unet': 'U-Net',
            'FADformer': 'FADformer',
            'MoCE-IR': 'MoCE-IR',
            'Moce-GAN': 'MoCE-GAN'
        }
        
        # 图表尺寸配置（单个图表）- 稍微缩小
        self.single_width = 1.1   # 英寸，单个IEEE图宽度（从1.25缩小到1.1）
        self.single_height = 1.4  # 英寸，IEEE图高度（从1.67缩小到1.4）
        self.pixel_width = 2.1    # 英寸，像素图宽度（从2.43缩小到2.1）
        self.pixel_height = 0.65  # 英寸，像素图高度（从0.75缩小到0.65）
        
        # DPI设置
        self.dpi = 1200
        
        # 颜色配置 - IEEE图保持原配色
        self.ieee_colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']  # 蓝、紫红、橙、红、深紫

        # 像素图配色 - 顶刊风格（更优雅的配色）
        self.pixel_colors = [
            '#000000',  # GT: 黑色
            '#1f77b4',  # CycleGAN: 经典蓝
            '#ff7f0e',  # U-Net: 橙色
            '#2ca02c',  # FADformer: 绿色
            '#d62728',  # MoCE-IR: 红色
            '#9467bd'   # MoCE-GAN: 紫色
        ]
        
        # 像素数据显示名称
        self.pixel_display_names = {
            'GT': 'GT',
            'Cycle_GAN': 'CycleGAN',
            'unet': 'U-Net',
            'FADformer': 'FADformer',
            'MoCE-IR': 'MoCE-IR',
            'Moce-GAN': 'MoCE-GAN'
        }
    
    def load_ieee_data(self, dataset_type: str = "peitai"):
        """加载IEEE数据"""
        print(f"📊 加载IEEE数据 - {dataset_type}数据集")

        all_data = []
        total_records = 0

        for model in self.models:
            # 查找模型目录下的CSV文件
            model_dir = self.reason_path / model
            if model_dir.exists():
                # 查找匹配的子目录 - 根据数据集选择尺度
                if dataset_type == "yiwu":
                    pattern = f"{dataset_type}_data16_256_inference_*"
                else:
                    pattern = f"{dataset_type}_data16_512_inference_*"
                matching_dirs = list(model_dir.glob(pattern))

                if matching_dirs:
                    # 使用第一个匹配的目录
                    inference_dir = matching_dirs[0]
                    csv_files = list(inference_dir.glob("inference_logs/*.csv"))

                    if csv_files:
                        csv_path = csv_files[0]  # 使用第一个CSV文件
                        df = pd.read_csv(csv_path)
                        df['Model'] = self.model_display_names[model]
                        all_data.append(df)
                        print(f"✅ 加载 {model}: {len(df)} 条记录")
                        total_records += len(df)
                    else:
                        print(f"⚠️ 未找到CSV文件: {inference_dir}")
                else:
                    print(f"⚠️ 未找到匹配目录: {model_dir}/{pattern}")
            else:
                print(f"⚠️ 模型目录不存在: {model_dir}")

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"📊 总计加载: {total_records} 条记录")
            return combined_df
        return None
    
    def extract_pixel_line(self, image_path, direction='vertical'):
        """从图片中提取像素线数据"""
        try:
            from PIL import Image
            import numpy as np

            if not image_path.exists():
                return None

            img = Image.open(image_path).convert('L')  # 转为灰度
            img_array = np.array(img) / 255.0  # 归一化到0-1

            if direction == 'vertical':
                # 提取中间列
                center_col = img_array.shape[1] // 2
                return img_array[:, center_col]
            else:  # horizontal
                # 提取中间行
                center_row = img_array.shape[0] // 2
                return img_array[center_row, :]

        except Exception as e:
            print(f"❌ 提取像素数据失败 {image_path}: {e}")
            return None

    def find_pixel_files(self, dataset_type: str = "peitai"):
        """动态查找像素数据文件"""
        filter_path = self.base_path / "visual" / "filter"
        found_files = {}

        # 查找GT文件（从任意模型目录中获取）- 根据数据集选择尺度
        scale_dir = "16_256" if dataset_type == "yiwu" else "16_512"
        for model in self.models:
            model_dir = filter_path / model / dataset_type / "local_comparison" / scale_dir
            if model_dir.exists():
                gt_files = list(model_dir.glob("*_GT_region1_enlarged.png"))
                if gt_files:
                    found_files['GT'] = gt_files[0]
                    print(f"✅ 找到GT文件: {gt_files[0].name}")
                    break

        # 查找各模型的Restored文件
        for model in self.models:
            model_dir = filter_path / model / dataset_type / "local_comparison" / scale_dir
            if model_dir.exists():
                restored_files = list(model_dir.glob("*_Restored_region1_enlarged.png"))
                if restored_files:
                    found_files[model] = restored_files[0]
                    print(f"✅ 找到{model}文件: {restored_files[0].name}")
                else:
                    print(f"⚠️ 未找到{model}的Restored文件")
            else:
                print(f"⚠️ 目录不存在: {model_dir}")

        return found_files

    def load_pixel_data(self, dataset_type: str = "peitai"):
        """加载真实的像素分布数据"""
        print(f"📊 加载真实像素数据 - {dataset_type}数据集")

        # 动态查找文件
        pixel_files = self.find_pixel_files(dataset_type)

        if not pixel_files:
            print("❌ 未找到任何像素数据文件")
            return None

        pixel_data = {}

        # 提取GT像素数据
        if 'GT' in pixel_files:
            gt_file = pixel_files['GT']
            gt_v = self.extract_pixel_line(gt_file, 'vertical')
            gt_h = self.extract_pixel_line(gt_file, 'horizontal')

            if gt_v is not None and gt_h is not None:
                pixel_data['GT'] = {
                    'vertical': gt_v,
                    'horizontal': gt_h
                }
                print(f"✅ 加载GT像素数据: {len(gt_v)} 像素点")

        # 提取各模型像素数据
        for model in self.models:
            if model in pixel_files:
                model_file = pixel_files[model]
                v_pixels = self.extract_pixel_line(model_file, 'vertical')
                h_pixels = self.extract_pixel_line(model_file, 'horizontal')

                if v_pixels is not None and h_pixels is not None:
                    pixel_data[model] = {
                        'vertical': v_pixels,
                        'horizontal': h_pixels
                    }
                    print(f"✅ 加载{self.pixel_display_names[model]}像素数据: {len(v_pixels)} 像素点")

        return pixel_data

    def calculate_optimal_y_range(self, pixel_data):
        """计算像素数据的最优Y轴范围"""
        all_values = []

        # 收集所有数据的像素值
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                for direction in ['vertical', 'horizontal']:
                    if direction in pixel_data[model_key]:
                        all_values.extend(pixel_data[model_key][direction])

        if not all_values:
            return 0, 1, [0, 0.5, 1.0]  # 默认范围

        all_values = np.array(all_values)

        # 计算数据范围
        data_min = np.min(all_values)
        data_max = np.max(all_values)
        data_range = data_max - data_min

        # 添加10%的边距
        margin = data_range * 0.1
        y_min = max(0, data_min - margin)
        y_max = min(1, data_max + margin)

        # 确保最小范围为0.2，避免过度放大噪声
        if (y_max - y_min) < 0.2:
            center = (y_min + y_max) / 2
            y_min = max(0, center - 0.1)
            y_max = min(1, center + 0.1)

        # 生成合适的刻度
        range_size = y_max - y_min
        if range_size <= 0.3:
            # 小范围：使用0.1间隔
            tick_interval = 0.1
        elif range_size <= 0.6:
            # 中等范围：使用0.2间隔
            tick_interval = 0.2
        else:
            # 大范围：使用0.25间隔
            tick_interval = 0.25

        # 计算刻度位置
        start_tick = np.ceil(y_min / tick_interval) * tick_interval
        end_tick = np.floor(y_max / tick_interval) * tick_interval
        y_ticks = np.arange(start_tick, end_tick + tick_interval/2, tick_interval)

        # 确保刻度在有效范围内
        y_ticks = y_ticks[(y_ticks >= 0) & (y_ticks <= 1)]

        print(f"📊 动态Y轴范围: [{y_min:.2f}, {y_max:.2f}], 刻度: {y_ticks}")

        return y_min, y_max, y_ticks

    def generate_single_ieee_chart(self, df, metric: str, dataset_type: str = "peitai"):
        """生成单个IEEE图表"""
        print(f"📊 生成{metric}图表...")
        
        # 创建单个图表
        fig, ax = plt.subplots(1, 1, figsize=(self.single_width, self.single_height))
        
        # 准备数据
        box_data = [df[df['Model'] == model][metric].values 
                   for model in [self.model_display_names[m] for m in self.models]]
        
        # 创建箱线图（无文字版本）
        bp = ax.boxplot(box_data, 
                       patch_artist=True, 
                       showfliers=False,
                       boxprops=dict(linewidth=0.8),
                       whiskerprops=dict(linewidth=0.8),
                       capprops=dict(linewidth=0.8),
                       medianprops=dict(linewidth=0.8))
        
        # 设置颜色
        for patch, color in zip(bp['boxes'], self.ieee_colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        # 设置刻度和标签（8pt字体）
        ax.tick_params(axis='both', labelsize=8)

        # 设置X轴刻度（模型名称）
        ax.set_xticks(range(1, len(self.models) + 1))
        ax.set_xticklabels([self.model_display_names[m] for m in self.models],
                          rotation=45, ha='right', fontsize=8, fontfamily='Times New Roman')
        # 确保刻度标签字体设置 - 黑色1磅
        ax.tick_params(axis='x', labelsize=8, color='black', width=1.0, length=4)
        ax.tick_params(axis='y', labelsize=8, color='black', width=1.0, length=4)

        # 设置Y轴范围和刻度 - 根据数据集调整
        if metric == 'PSNR':
            if dataset_type == 'yiwu':
                ax.set_ylim(10, 40)  # yiwu数据集PSNR范围 - 扩大下限到10
                ax.set_yticks(range(10, 41, 5))
            elif dataset_type == 'bone':
                ax.set_ylim(15, 35)  # bone数据集PSNR范围 - 15到35
                ax.set_yticks(range(15, 36, 5))  # 15, 20, 25, 30, 35
            else:
                ax.set_ylim(20, 40)  # peitai等其他数据集
                ax.set_yticks(range(20, 41, 5))
        elif metric == 'SSIM':
            ax.set_ylim(0.6, 1.0)
            ax.set_yticks([0.6, 0.7, 0.8, 0.9, 1.0])
        elif metric == 'LPIPS':
            if dataset_type == 'yiwu':
                ax.set_ylim(0, 0.6)  # yiwu数据集LPIPS范围更大
                ax.set_yticks([0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6])
            else:
                ax.set_ylim(0, 0.4)  # bone等其他数据集
                ax.set_yticks([0, 0.1, 0.2, 0.3, 0.4])

        # 添加网格 - 顶刊风格
        ax.grid(True, alpha=0.25, linewidth=0.4, color='#CCCCCC', linestyle='-')
        ax.set_axisbelow(True)

        # 边框样式 - 黑色1磅，四边完整
        for spine in ax.spines.values():
            spine.set_linewidth(1.0)  # 1磅线宽
            spine.set_color('black')  # 黑色
            spine.set_visible(True)   # 确保所有边框可见
        
        # 保存
        output_file = self.output_path / f"ieee_{metric.lower()}_no_text_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', pad_inches=0)
        plt.close()
        
        print(f"✅ {metric}图表已保存: {output_file}")
        return True
    
    def generate_single_pixel_chart(self, pixel_data, chart_type: str, dataset_type: str = "peitai", y_range=None):
        """生成单个像素分布图"""
        print(f"📈 生成{chart_type}像素分布图...")

        # 创建单个图表
        fig, ax = plt.subplots(1, 1, figsize=(self.pixel_width, self.pixel_height))

        # 绘制数据 - 顶刊风格
        color_idx = 0
        legend_lines = []  # 存储图例线条
        legend_labels = []  # 存储图例标签

        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                data = pixel_data[model_key][chart_type]
                x_positions = np.arange(len(data))

                # 顶刊风格线条设置
                if model_key == 'GT':
                    linewidth = 1.2  # GT稍粗一些
                    alpha = 1.0      # GT完全不透明
                    label = 'GT'
                else:
                    linewidth = 0.8  # 其他模型更细
                    alpha = 0.85     # 稍微透明
                    label = self.pixel_display_names[model_key]

                line = ax.plot(x_positions, data,
                              color=self.pixel_colors[color_idx],
                              linewidth=linewidth,
                              alpha=alpha,
                              solid_capstyle='round',  # 圆润线条端点
                              solid_joinstyle='round', # 圆润线条连接
                              label=label)[0]

                legend_lines.append(line)
                legend_labels.append(label)
                color_idx += 1

        # 设置刻度和标签（8pt字体）
        ax.tick_params(axis='both', labelsize=8)

        # 设置X轴刻度 - 固定为0, 30, 60, 90, 120
        ax.set_xlim(0, 120)
        ax.set_xticks([0, 30, 60, 90, 120])
        # 确保X轴刻度标签字体大小为8pt - 黑色1磅
        ax.tick_params(axis='x', labelsize=8, color='black', width=1.0, length=4)

        # 使用传入的Y轴范围或计算动态范围
        if y_range is not None:
            y_min, y_max, y_ticks = y_range
            ax.set_ylim(y_min, y_max)
            ax.set_yticks(y_ticks)
        else:
            # 默认范围
            ax.set_ylim(0, 1)
            ax.set_yticks([0, 0.5, 1.0])

        # 确保Y轴刻度标签字体大小为8pt - 黑色1磅
        ax.tick_params(axis='y', labelsize=8, color='black', width=1.0, length=4)

        # 添加网格 - 顶刊风格
        ax.grid(True, alpha=0.25, linewidth=0.4, color='#CCCCCC', linestyle='-')
        ax.set_axisbelow(True)

        # 边框样式 - 黑色1磅，四边完整
        for spine in ax.spines.values():
            spine.set_linewidth(1.0)  # 1磅线宽
            spine.set_color('black')  # 黑色
            spine.set_visible(True)   # 确保所有边框可见

        # 刻度样式 - 黑色1磅
        ax.tick_params(axis='both', which='major',
                      labelsize=8, color='black', width=1.0, length=4)

        # 添加图例 - 右侧外部，不遮挡数据
        if legend_lines:
            if chart_type == 'vertical':
                # 垂直图显示前3个（GT + 前2个模型）
                legend_items = legend_lines[:3]
                legend_names = legend_labels[:3]
            else:
                # 水平图显示后3个（后3个模型）
                legend_items = legend_lines[3:]
                legend_names = legend_labels[3:]

            # 图例设置 - 真正紧贴右上角
            legend = ax.legend(legend_items, legend_names,
                             loc='upper right',  # 右上角
                             bbox_to_anchor=(0.98, 0.98),  # 稍微内移，避免被裁切
                             frameon=True,  # 有边框
                             fancybox=False,  # 方形边框
                             shadow=False,  # 无阴影
                             framealpha=0.95,  # 高透明度背景
                             edgecolor='#DDDDDD',  # 淡边框颜色
                             facecolor='white',  # 白色背景
                             fontsize=5.5,  # 更小字体
                             handlelength=0.6,  # 更短的图例线条
                             handletextpad=0.2,  # 更紧凑的线条和文字间距
                             borderpad=0.05,  # 极小内边距
                             labelspacing=0.05,  # 极紧凑的行间距
                             borderaxespad=0.0)  # 消除图例与轴的间距

            # 设置图例字体和边框样式
            for text in legend.get_texts():
                text.set_fontfamily('Times New Roman')

            # 设置边框线宽
            legend.get_frame().set_linewidth(0.5)
        
        # 保存
        output_file = self.output_path / f"pixel_{chart_type}_no_text_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none', pad_inches=0)
        plt.close()
        
        print(f"✅ {chart_type}像素分布图已保存: {output_file}")
        return True
    
    def generate_all_individual_charts(self, dataset_type: str = "peitai"):
        """生成所有独立图表"""
        print("🎨 生成独立的无文字图表")
        print("=" * 50)
        print(f"📊 数据集: {dataset_type}")
        print(f"📊 IEEE单图尺寸: {self.single_width:.2f}\" × {self.single_height:.2f}\"")
        print(f"📊 像素图尺寸: {self.pixel_width:.2f}\" × {self.pixel_height:.2f}\"")
        print(f"📊 输出DPI: {self.dpi}")
        print("=" * 50)
        
        # 加载IEEE数据
        ieee_df = self.load_ieee_data(dataset_type)
        if ieee_df is None or ieee_df.empty:
            print("❌ 无法加载IEEE数据")
            return False
        
        # 生成3个IEEE图表
        for metric in ['PSNR', 'SSIM', 'LPIPS']:
            self.generate_single_ieee_chart(ieee_df, metric, dataset_type)
        
        # 加载像素数据
        pixel_data = self.load_pixel_data(dataset_type)
        if pixel_data is None or len(pixel_data) == 0:
            print("❌ 无法加载像素数据")
            return False

        # 计算统一的Y轴范围（确保vertical和horizontal使用相同范围）
        y_min, y_max, y_ticks = self.calculate_optimal_y_range(pixel_data)
        y_range = (y_min, y_max, y_ticks)

        # 生成2个像素分布图，使用相同的Y轴范围
        for chart_type in ['vertical', 'horizontal']:
            self.generate_single_pixel_chart(pixel_data, chart_type, dataset_type, y_range)
        
        print("\n🎉 所有独立图表生成完成！")
        print(f"📁 输出目录: {self.output_path}")
        print("📊 生成的图表:")
        print("   - ieee_psnr_no_text_peitai.png")
        print("   - ieee_ssim_no_text_peitai.png")
        print("   - ieee_lpips_no_text_peitai.png")
        print("   - pixel_vertical_no_text_peitai.png")
        print("   - pixel_horizontal_no_text_peitai.png")
        
        return True

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        dataset = sys.argv[1]
        if dataset not in ['bone', 'peitai', 'yiwu']:
            print(f"❌ 不支持的数据集: {dataset}")
            print("✅ 支持的数据集: bone, peitai, yiwu")
            return
    else:
        dataset = "bone"  # 默认数据集

    generator = IndividualChartGenerator()
    generator.generate_all_individual_charts(dataset)

if __name__ == "__main__":
    main()
