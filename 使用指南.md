# 🔬 可视化系统使用指南

## ✅ Windows适配完成

您的项目已成功从Linux环境迁移到Windows环境！所有路径和配置都已更新。

## 🚀 快速开始

### 1. 检查环境
```powershell
# 检查Python环境是否正常
C:\Users\<USER>\.conda\envs\visual_env\python.exe -c "print('✅ Python环境正常')"

# 检查依赖包
C:\Users\<USER>\.conda\envs\visual_env\python.exe -c "import pandas, numpy, matplot<PERSON>b, seaborn, PIL, cv2; print('✅ 所有依赖包正常')"
```

### 2. 运行可视化（推荐顺序）

#### 第一步：生成IEEE风格图表
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\run.py
```
**输出**: `ieee_style_peitai_16_512.png`, `ieee_style_bone_16_512.png`

#### 第二步：多模型可视化分析
```powershell
# 胚胎数据集
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\demo\multi_model_visualizer.py --dataset_type peitai --copy_top_rank 1

# 骨骼数据集  
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\demo\multi_model_visualizer.py --dataset_type bone --copy_top_rank 1
```
**输出**: 小提琴图、最佳重建对比、局部放大对比

#### 第三步：像素分布图和虚线分析
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\line_analyzer.py
```
**输出**: 像素分布图、带虚线的放大图

#### 第四步：查看分析总结
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\analysis_summary.py
```

## 📁 输出文件位置

```
E:\reasoning\visual\
├── AI_pingtu\                 # IEEE图表和像素分布图
│   ├── ieee_style_*.png      # IEEE风格图表
│   └── pixel_distribution_*.png  # 像素分布图
├── enlarged_with_lines\       # 带虚线的放大图
│   ├── CycleGAN\             # 各模型的虚线标记图
│   ├── U-Net\
│   ├── FADformer\
│   ├── MoCE-IR\
│   ├── MoCE-GAN\
│   └── GT\                   # GT图像的虚线标记图
├── data\                     # 完整可视化结果
└── filter\                   # 筛选的top图片
```

## 🎯 主要功能

1. **IEEE风格图表** - 学术论文标准图表
2. **多模型对比** - 5个模型性能对比
3. **局部分析** - 详细区域对比
4. **像素分布** - 定量分析图表

## 💡 使用技巧

- **修改top排名**: 改变 `--copy_top_rank` 参数（1-10）
- **单独数据集**: 使用 `--dataset_type peitai` 或 `bone`
- **高质量输出**: 所有图片均为600 DPI

## 🔧 如需安装依赖

```powershell
C:\Users\<USER>\.conda\envs\visual_env\Scripts\pip.exe install pandas numpy matplotlib seaborn pillow opencv-python
```

---

**状态**: ✅ 已完成Windows适配  
**环境**: visual_env conda环境  
**路径**: E:\reasoning
