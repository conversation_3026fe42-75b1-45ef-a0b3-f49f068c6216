# 🎯 PPT控制器 - COM接口版

## 🎯 功能说明

使用COM接口实时操作PowerPoint，专门针对`pingtu.pptx`文件进行优化。
可以在PPT打开的状态下直接添加、修改内容，无需关闭文件。

## 📁 文件说明

- `ppt_controller.py` - 主要的PPT控制器（COM接口）
- `quick_update.py` - 快速添加图表脚本
- `test_com_live.py` - COM接口测试脚本
- `pingtu.pptx` - 目标PPT文件

## 🚀 使用方法

### 方法一：完整功能（推荐）
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe ppt_demo\ppt_controller.py
```

### 方法二：快速添加图表
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe ppt_demo\quick_update.py
```

### 方法三：测试COM接口
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe ppt_demo\test_com_live.py
```

## 📋 插入的图片

1. **IEEE风格图表**: `ieee_style_peitai_16_512.png`
   - 来源：`E:\reasoning\visual\AI_pingtu\`
   - 内容：PSNR、SSIM、LPIPS对比图表

2. **像素分布图**: `pixel_distribution_peitai_16_512.png`
   - 来源：`E:\reasoning\visual\AI_pingtu\`
   - 内容：沿虚线的像素强度分布分析

## 🎨 布局特点

- **PPT尺寸**: 17.2cm × 10cm
- **图片位置**: 页面底部并排放置
- **自动缩放**: 确保图片不超过画布边界
- **智能布局**: 自动计算最佳尺寸和位置
- **安全边距**: 保留0.5cm边距，图片间距0.2cm

## 🔧 技术细节

### 依赖包
- `pywin32` - Windows COM接口
- `Pillow` - 图片处理（如需要）

### COM接口特点
1. 实时操作PowerPoint应用程序
2. 无需关闭PPT文件
3. 直接在打开的PPT中显示变化
4. 支持文本、形状、图片等各种内容

### 核心功能
- 连接到PowerPoint应用程序
- 查找并操作pingtu.pptx
- 清空幻灯片内容
- 添加标题和文本
- 插入IEEE图表和像素分布图
- 实时保存更改

## 📊 运行示例

```
🔬 PPT图片插入工具
============================================================
📊 准备插入的图片:
   - IEEE风格图表: E:\reasoning\visual\AI_pingtu\ieee_style_peitai_16_512.png
   - 像素分布图: E:\reasoning\visual\AI_pingtu\pixel_distribution_peitai_16_512.png

成功打开PPT文件: E:\reasoning\ppt_demo\pingtu.pptx
PPT尺寸: 6.77" x 3.94"
正在处理第1张幻灯片
IEEE图片原始尺寸: 9.72" x 3.52"
像素分布图原始尺寸: 8.74" x 7.07"
✅ 成功插入IEEE图表: ieee_style_peitai_16_512.png
   位置: (0.6cm, 6.9cm)
   尺寸: 7.9cm x 2.9cm
✅ 成功插入像素分布图: pixel_distribution_peitai_16_512.png
   位置: (8.7cm, 3.4cm)
   尺寸: 7.9cm x 6.4cm
✅ PPT文件已保存: E:\reasoning\ppt_demo\pingtu.pptx
📁 备份文件: E:\reasoning\ppt_demo\pingtu.backup.pptx

============================================================
🎉 图片插入完成！
```

## ⚠️ 注意事项

1. **关闭PowerPoint**: 运行前请确保PPT文件未被PowerPoint打开
2. **备份文件**: 每次运行都会自动创建备份文件
3. **图片路径**: 确保源图片文件存在于指定路径
4. **PPT格式**: 支持.pptx格式文件

## 🔄 更新图片

如果需要更新不同的图片，可以修改`insert_images_to_ppt.py`中的文件名：

```python
# 图片文件名
self.ieee_image = "ieee_style_peitai_16_512.png"
self.pixel_image = "pixel_distribution_peitai_16_512.png"
```

## 📞 故障排除

1. **PPT文件被锁定**: 关闭PowerPoint后重试
2. **图片文件不存在**: 检查图片路径是否正确
3. **权限问题**: 确保有写入PPT文件的权限
4. **依赖包缺失**: 运行`pip install python-pptx`

---

**版本**: v1.0  
**更新日期**: 2025-07-21  
**兼容性**: Windows + visual_env环境
