# 模型性能统计分析报告

生成时间: 2025-07-24 16:24:06

## 分析概述

- **总实验数量**: 70
- **模型数量**: 5
- **数据集数量**: 3
- **总样本数量**: 21250

## 评估指标

- **PSNR**: 峰值信噪比，值越高表示重建质量越好
- **SSIM**: 结构相似性指数，值越高表示结构保持越好
- **LPIPS**: 感知图像补丁相似性，值越低表示感知质量越好

## 模型列表

- CycleGAN
- FADformer
- MoCE-GAN
- MoCE-IR
- U-Net

## 数据集列表

- Bone
- Peitai
- Yiwu

## 文件结构

```
tongji/
├── model_performance_statistics.py    # 主分析脚本
├── raw_experiment_data.csv           # 原始实验数据
├── model_performance_summary.csv     # 汇总统计数据
├── three_line_table_data.csv         # 综合三线表格式数据
├── bone_performance_table.csv        # Bone数据集性能表
├── peitai_performance_table.csv      # Peitai数据集性能表
├── yiwu_performance_table.csv        # Yiwu数据集性能表
└── performance_analysis_report.md    # 分析报告
```

## 使用说明

1. **raw_experiment_data.csv**: 包含所有实验的详细信息
2. **model_performance_summary.csv**: 按模型和数据集分组的统计信息
3. **three_line_table_data.csv**: 综合三线表格式数据（所有数据集）
4. **{dataset}_performance_table.csv**: 单独数据集的性能表格，包含均值和标准差

