#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT控制器 - 原尺寸插入版本
不缩放图片，直接按原尺寸插入到PPT中
专门操作 pintu2.pptx
"""

import win32com.client
from pathlib import Path

class NoScalePPTController:
    """原尺寸插入PPT控制器"""
    
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.ppt_path = self.base_path / "ppt_demo" / "pintu2.pptx"
        self.image_path = self.base_path / "ppt_demo"
        
        self.ppt_app = None
        self.presentation = None
        
        # 无文字版本的图片路径
        self.ieee_image = self.image_path / "ieee_chart_no_text_peitai.png"
        self.pixel_image = self.image_path / "pixel_distribution_no_text_peitai.png"
    
    def connect(self):
        """连接到PowerPoint并找到pintu2.pptx"""
        try:
            print("🔗 连接到PowerPoint...")
            self.ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            
            # 查找pintu2.pptx
            target_name = "pintu2.pptx"
            for i in range(1, self.ppt_app.Presentations.Count + 1):
                pres = self.ppt_app.Presentations(i)
                if Path(pres.Name).name == target_name:
                    self.presentation = pres
                    print(f"✅ 找到 {target_name}")
                    return True
            
            # 如果没找到，尝试打开
            if self.ppt_path.exists():
                print(f"📂 打开 {target_name}...")
                self.presentation = self.ppt_app.Presentations.Open(str(self.ppt_path))
                print(f"✅ 已打开 {target_name}")
                return True
            else:
                print(f"❌ 文件不存在: {self.ppt_path}")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_slide(self, slide_num=1):
        """获取指定幻灯片"""
        if not self.presentation:
            return None
        return self.presentation.Slides(slide_num)
    
    def clear_slide(self, slide_num=1):
        """清空指定幻灯片的所有内容"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False
            
            print(f"🧹 清空幻灯片 {slide_num}...")
            
            # 从后往前删除所有形状
            for i in range(slide.Shapes.Count, 0, -1):
                slide.Shapes(i).Delete()
            
            print(f"✅ 幻灯片 {slide_num} 已清空")
            return True
            
        except Exception as e:
            print(f"❌ 清空失败: {e}")
            return False
    
    def measure_ppt_size(self):
        """测量PPT的实际尺寸"""
        if not self.presentation:
            return None
        
        # 获取PPT尺寸（单位：点）
        slide_width = self.presentation.PageSetup.SlideWidth
        slide_height = self.presentation.PageSetup.SlideHeight
        
        # 转换为厘米 (1点 = 0.0352778厘米)
        width_cm = slide_width * 0.0352778
        height_cm = slide_height * 0.0352778
        
        print(f"📏 PPT尺寸测量:")
        print(f"   - 点数: {slide_width:.0f} × {slide_height:.0f} 点")
        print(f"   - 厘米: {width_cm:.2f} × {height_cm:.2f} cm")
        
        return {
            'width_points': slide_width,
            'height_points': slide_height,
            'width_cm': width_cm,
            'height_cm': height_cm
        }
    
    def add_charts_original_size(self, slide_num=1):
        """按原尺寸添加图表到幻灯片底部"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False
            
            print("📊 添加原尺寸图表...")
            
            # 测量PPT尺寸
            ppt_size = self.measure_ppt_size()
            if not ppt_size:
                return False
            
            slide_width = ppt_size['width_points']
            slide_height = ppt_size['height_points']
            
            # 检查图片文件是否存在
            if not self.ieee_image.exists():
                print(f"❌ IEEE图表文件不存在: {self.ieee_image}")
                return False
                
            if not self.pixel_image.exists():
                print(f"❌ 像素分布图文件不存在: {self.pixel_image}")
                return False
            
            # 计算图片的原始尺寸（点）
            # IEEE图表: 270点宽 × 120点高
            # 像素分布图: 175点宽 × 120点高
            ieee_width = 270
            ieee_height = 120
            pixel_width = 175
            pixel_height = 120
            
            # 计算总宽度和检查是否超出
            gap = 10  # 图表间距
            total_width = ieee_width + pixel_width + gap
            
            print(f"📐 尺寸计算:")
            print(f"   - IEEE图表: {ieee_width} × {ieee_height} 点")
            print(f"   - 像素分布图: {pixel_width} × {pixel_height} 点")
            print(f"   - 总宽度: {total_width} 点")
            print(f"   - PPT宽度: {slide_width:.0f} 点")
            
            if total_width > slide_width:
                print(f"⚠️ 警告: 图表总宽度({total_width}点) 超过PPT宽度({slide_width:.0f}点)")
                # 调整间距
                gap = max(5, slide_width - ieee_width - pixel_width)
                total_width = ieee_width + pixel_width + gap
                print(f"🔧 调整间距为: {gap} 点")
            
            # 计算位置（底部居中）
            margin = 10  # 底部边距
            start_x = (slide_width - total_width) / 2
            
            # IEEE图表位置（左下）
            ieee_left = start_x
            ieee_top = slide_height - ieee_height - margin
            
            # 像素分布图位置（右下）
            pixel_left = start_x + ieee_width + gap
            pixel_top = slide_height - pixel_height - margin
            
            print(f"📍 位置计算:")
            print(f"   - IEEE图表位置: ({ieee_left:.0f}, {ieee_top:.0f})")
            print(f"   - 像素分布图位置: ({pixel_left:.0f}, {pixel_top:.0f})")
            
            # 添加IEEE图表（原尺寸）
            slide.Shapes.AddPicture(
                FileName=str(self.ieee_image),
                LinkToFile=False,
                SaveWithDocument=True,
                Left=ieee_left,
                Top=ieee_top,
                Width=ieee_width,
                Height=ieee_height
            )
            print(f"✅ IEEE图表已添加 (原尺寸: {ieee_width}×{ieee_height}点)")
            
            # 添加像素分布图（原尺寸）
            slide.Shapes.AddPicture(
                FileName=str(self.pixel_image),
                LinkToFile=False,
                SaveWithDocument=True,
                Left=pixel_left,
                Top=pixel_top,
                Width=pixel_width,
                Height=pixel_height
            )
            print(f"✅ 像素分布图已添加 (原尺寸: {pixel_width}×{pixel_height}点)")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加图表失败: {e}")
            return False
    
    def get_info(self):
        """获取PPT信息"""
        if not self.presentation:
            print("❌ 未连接到演示文稿")
            return
        
        print("📊 PPT信息:")
        print(f"   - 文件名: {self.presentation.Name}")
        print(f"   - 幻灯片数量: {self.presentation.Slides.Count}")
        
        ppt_size = self.measure_ppt_size()
        if ppt_size:
            print(f"   - 尺寸: {ppt_size['width_points']:.0f} × {ppt_size['height_points']:.0f} 点")
            print(f"   - 尺寸: {ppt_size['width_cm']:.2f} × {ppt_size['height_cm']:.2f} cm")
        
        if self.presentation.Slides.Count > 0:
            slide = self.presentation.Slides(1)
            print(f"   - 第1张幻灯片形状数: {slide.Shapes.Count}")
    
    def save(self):
        """保存PPT"""
        try:
            if self.presentation:
                self.presentation.Save()
                print("💾 PPT已保存")
                return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
        return False

def main():
    """主函数 - 原尺寸插入图表"""
    print("🎯 原尺寸PPT图表控制器")
    print("=" * 40)
    
    controller = NoScalePPTController()
    
    # 连接
    if not controller.connect():
        return
    
    # 显示信息
    controller.get_info()
    
    print("\n🎨 开始操作...")
    
    # 清空幻灯片
    controller.clear_slide()
    
    # 添加原尺寸图表
    controller.add_charts_original_size()
    
    # 保存
    controller.save()
    
    print("\n✅ 原尺寸图表插入完成！")
    print("📊 特点:")
    print("   - 图表按原始尺寸插入，无缩放")
    print("   - 无任何文字标签，便于后续编辑")
    print("   - 底部并排放置，不超过PPT宽度")

if __name__ == "__main__":
    main()
