# 🔬 可视化分析系统 - Windows版本

## 📋 项目概述

这是一个专业的学术图表可视化系统，已从Linux环境成功迁移到Windows环境。系统包含三个主要功能模块：

1. **IEEE风格指标图** - 生成符合学术标准的性能对比图表
2. **多模型可视化分析** - 生成小提琴图、最佳重建对比、局部放大对比
3. **像素分布图和虚线分析** - 生成像素强度分析和带标记的放大图

## 🎯 主要功能

### 1. IEEE风格指标图
- **输出文件**: `ieee_style_peitai_16_512.png`, `ieee_style_bone_16_512.png`
- **特点**: 1行3列布局 (PSNR | SSIM | LPIPS)
- **模型顺序**: CycleGAN → U-Net → FADformer → MoCE-IR → MoCE-GAN
- **质量**: 600 DPI高质量输出，适合学术发表

### 2. 多模型可视化分析
- **小提琴图**: 展示各模型在不同尺度下的性能分布
- **最佳重建对比**: 三种分割样式（对角线、水平、梯形）
- **局部放大对比**: 详细的区域对比分析
- **数据集**: 支持胚胎(peitai)和骨骼(bone)两种数据集

### 3. 像素分布图和虚线分析
- **像素分布图**: 沿虚线的像素强度分布分析
- **带虚线放大图**: 标记分析位置的放大图像
- **分析类型**: 垂直和水平两个方向的像素分布

## 🚀 快速开始

### 方式一：完整运行（推荐）
在PowerShell或命令提示符中依次运行以下命令：

```powershell
# 1. IEEE风格图表生成
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\run.py

# 2. 多模型可视化（胚胎数据集）
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\demo\multi_model_visualizer.py --dataset_type peitai --copy_top_rank 1

# 3. 多模型可视化（骨骼数据集）
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\demo\multi_model_visualizer.py --dataset_type bone --copy_top_rank 1

# 4. 像素分布图和虚线分析
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\line_analyzer.py
```

### 方式二：快速预览（只生成IEEE风格图表）
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\run.py
```

### 方式三：单独运行特定功能
```powershell
# 多模型可视化（指定数据集和排名）
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\demo\multi_model_visualizer.py --dataset_type peitai --copy_top_rank 5

# 查看分析总结
C:\Users\<USER>\.conda\envs\visual_env\python.exe visual\AI_pingtu\analysis_summary.py
```

## 📁 输出文件结构

```
E:\reasoning\
├── visual\
│   ├── AI_pingtu\                       # IEEE风格图表和像素分布图
│   │   ├── ieee_style_peitai_16_512.png
│   │   ├── ieee_style_bone_16_512.png
│   │   ├── pixel_distribution_peitai_16_512.png
│   │   └── pixel_distribution_bone_16_512.png
│   ├── enlarged_with_lines\             # 带虚线的放大图
│   │   ├── GT\
│   │   ├── CycleGAN\
│   │   ├── U-Net\
│   │   ├── FADformer\
│   │   ├── MoCE-IR\
│   │   └── MoCE-GAN\
│   ├── data\                           # 完整的多模型可视化结果
│   │   ├── Moce-GAN\
│   │   ├── MoCE-IR\
│   │   ├── Cycle_GAN\
│   │   ├── FADformer\
│   │   └── unet\
│   └── filter\                         # 筛选的top图片
│       └── (同data结构)
```

## ⚙️ 环境要求

### 必需环境
- **操作系统**: Windows 10/11
- **Python环境**: conda环境 `visual_env`
- **Python版本**: 3.8+

### 依赖包
```
pandas
numpy
matplotlib
seaborn
pillow (PIL)
opencv-python (cv2)
pathlib
argparse
```

## 🔧 环境配置

### 1. 检查conda环境
```batch
conda env list
```
确保存在 `visual_env` 环境

### 2. 检查依赖包
```powershell
C:\Users\<USER>\.conda\envs\visual_env\python.exe -c "import pandas, numpy, matplotlib, seaborn, PIL, cv2; print('✅ 所有依赖包检查通过')"
```

### 3. 如需安装依赖
```powershell
C:\Users\<USER>\.conda\envs\visual_env\Scripts\pip.exe install pandas numpy matplotlib seaborn pillow opencv-python
```

## 📊 使用场景

### 学术发表
- IEEE风格图表符合顶级期刊标准
- 600 DPI高质量输出
- 专业的字体和布局设计

### 研究分析
- 多模型性能对比
- 详细的局部区域分析
- 定量的像素分布分析

### 演示展示
- 高分辨率图表适合大屏展示
- 清晰的视觉效果
- 专业美观的设计

## 🎨 技术特点

- **高质量输出**: 所有图表均为600 DPI
- **专业设计**: 符合IEEE学术标准
- **多格式支持**: 同时生成PNG和SVG格式
- **自动化处理**: 一键生成所有图表
- **Windows优化**: 完全适配Windows环境

## 🔍 故障排除

### 常见问题

1. **conda环境激活失败**
   - 确保已安装Anaconda/Miniconda
   - 检查visual_env环境是否存在

2. **依赖包导入失败**
   - 运行依赖检查命令
   - 使用pip安装缺失的包

3. **路径错误**
   - 确保在E:\reasoning目录下运行
   - 检查数据文件是否存在

4. **内存不足**
   - 关闭其他程序释放内存
   - 可以分别运行各个模块

## 📞 技术支持

如遇到问题，请检查：
1. conda环境是否正确激活
2. 所有依赖包是否已安装
3. 数据文件路径是否正确
4. 系统内存是否充足

---

**版本**: Windows适配版 v1.0  
**更新日期**: 2025-07-21  
**兼容性**: Windows 10/11 + conda环境
