#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT控制器 - 独立图表版本
将5个独立图表排列到PPT中
左边3个IEEE图（上中下），右边2个像素图（上下）
"""

import win32com.client
from pathlib import Path

class IndividualChartsPPTController:
    """独立图表PPT控制器"""
    
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.ppt_path = self.base_path / "ppt_demo" / "ppt4.pptx"  # 新的PPT文件
        self.image_path = self.base_path / "ppt_demo"
        
        self.ppt_app = None
        self.presentation = None
        
        # 5个独立图片路径 - yiwu数据集
        self.ieee_psnr = self.image_path / "ieee_psnr_no_text_yiwu.png"
        self.ieee_ssim = self.image_path / "ieee_ssim_no_text_yiwu.png"
        self.ieee_lpips = self.image_path / "ieee_lpips_no_text_yiwu.png"
        self.pixel_vertical = self.image_path / "pixel_vertical_no_text_yiwu.png"
        self.pixel_horizontal = self.image_path / "pixel_horizontal_no_text_yiwu.png"
        
        # 图片尺寸（点）- 稍微缩小
        self.ieee_width = 79   # 单个IEEE图宽度（1.1英寸 * 72点/英寸）
        self.ieee_height = 101  # IEEE图高度（1.4英寸 * 72点/英寸）
        self.pixel_width = 151  # 像素图宽度（2.1英寸 * 72点/英寸）
        self.pixel_height = 47  # 像素图高度（0.65英寸 * 72点/英寸）
    
    def connect(self):
        """连接到PowerPoint并创建/打开ppt4.pptx"""
        try:
            print("🔗 连接到PowerPoint...")
            self.ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            
            # 查找ppt4.pptx
            target_name = "ppt4.pptx"
            for i in range(1, self.ppt_app.Presentations.Count + 1):
                pres = self.ppt_app.Presentations(i)
                if Path(pres.Name).name == target_name:
                    self.presentation = pres
                    print(f"✅ 找到 {target_name}")
                    return True
            
            # 如果没找到，尝试打开或创建
            if self.ppt_path.exists():
                print(f"📂 打开 {target_name}...")
                self.presentation = self.ppt_app.Presentations.Open(str(self.ppt_path))
                print(f"✅ 已打开 {target_name}")
            else:
                print(f"📝 创建新的 {target_name}...")
                self.presentation = self.ppt_app.Presentations.Add()
                self.presentation.SaveAs(str(self.ppt_path))
                print(f"✅ 已创建 {target_name}")
            
            return True
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_slide(self, slide_num=1):
        """获取指定幻灯片"""
        if not self.presentation:
            return None
        
        # 确保有足够的幻灯片
        while self.presentation.Slides.Count < slide_num:
            self.presentation.Slides.Add(self.presentation.Slides.Count + 1, 12)  # 12 = ppLayoutBlank
        
        return self.presentation.Slides(slide_num)
    
    def clear_slide(self, slide_num=1):
        """清空指定幻灯片的所有内容"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False
            
            print(f"🧹 清空幻灯片 {slide_num}...")
            
            # 从后往前删除所有形状
            for i in range(slide.Shapes.Count, 0, -1):
                slide.Shapes(i).Delete()
            
            print(f"✅ 幻灯片 {slide_num} 已清空")
            return True
            
        except Exception as e:
            print(f"❌ 清空失败: {e}")
            return False
    
    def measure_ppt_size(self):
        """测量PPT的实际尺寸"""
        if not self.presentation:
            return None
        
        # 获取PPT尺寸（单位：点）
        slide_width = self.presentation.PageSetup.SlideWidth
        slide_height = self.presentation.PageSetup.SlideHeight
        
        # 转换为厘米 (1点 = 0.0352778厘米)
        width_cm = slide_width * 0.0352778
        height_cm = slide_height * 0.0352778
        
        print(f"📏 PPT尺寸测量:")
        print(f"   - 点数: {slide_width:.0f} × {slide_height:.0f} 点")
        print(f"   - 厘米: {width_cm:.2f} × {height_cm:.2f} cm")
        
        return {
            'width_points': slide_width,
            'height_points': slide_height,
            'width_cm': width_cm,
            'height_cm': height_cm
        }
    
    def add_individual_charts(self, slide_num=1):
        """添加5个独立图表到幻灯片"""
        try:
            slide = self.get_slide(slide_num)
            if not slide:
                return False
            
            print("📊 添加5个独立图表...")
            
            # 测量PPT尺寸
            ppt_size = self.measure_ppt_size()
            if not ppt_size:
                return False
            
            slide_width = ppt_size['width_points']
            slide_height = ppt_size['height_points']
            
            # 检查所有图片文件是否存在
            images = [
                (self.ieee_psnr, "IEEE PSNR"),
                (self.ieee_ssim, "IEEE SSIM"),
                (self.ieee_lpips, "IEEE LPIPS"),
                (self.pixel_vertical, "像素垂直分析"),
                (self.pixel_horizontal, "像素水平分析")
            ]
            
            for img_path, name in images:
                if not img_path.exists():
                    print(f"❌ 图片文件不存在: {img_path}")
                    return False
            
            # 计算布局 - 底部排列
            margin = 10  # 边距
            gap = 8      # 图片间距
            bottom_margin = 15  # 底部边距

            # 左侧3个IEEE图的布局（水平并列，底部对齐）
            ieee_total_width = 3 * self.ieee_width + 2 * gap
            ieee_start_x = margin
            ieee_y = slide_height - self.ieee_height - bottom_margin  # 底部对齐

            # 右侧2个像素图的布局（垂直排列，底部对齐）
            pixel_start_x = ieee_start_x + ieee_total_width + gap * 2
            pixel_total_height = 2 * self.pixel_height + gap
            pixel_start_y = slide_height - pixel_total_height - bottom_margin  # 底部对齐
            
            print(f"📐 布局计算:")
            print(f"   - 左侧IEEE图起始位置: ({ieee_start_x}, {ieee_y:.0f})")
            print(f"   - 右侧像素图起始位置: ({pixel_start_x}, {pixel_start_y:.0f})")
            print(f"   - IEEE图尺寸: {self.ieee_width} × {self.ieee_height}")
            print(f"   - 像素图尺寸: {self.pixel_width} × {self.pixel_height}")
            print(f"   - IEEE图水平并列，像素图垂直排列，都在底部")
            print(f"   - 底部边距: {bottom_margin}点")
            
            # 添加左侧3个IEEE图（水平并列）
            ieee_images = [
                (self.ieee_psnr, "PSNR"),
                (self.ieee_ssim, "SSIM"),
                (self.ieee_lpips, "LPIPS")
            ]

            for i, (img_path, name) in enumerate(ieee_images):
                x_pos = ieee_start_x + i * (self.ieee_width + gap)
                slide.Shapes.AddPicture(
                    FileName=str(img_path),
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=x_pos,
                    Top=ieee_y,
                    Width=self.ieee_width,
                    Height=self.ieee_height
                )
                print(f"✅ {name}图表已添加 (位置: {x_pos:.0f}, {ieee_y:.0f})")
            
            # 添加右侧2个像素图（垂直排列）
            pixel_images = [
                (self.pixel_vertical, "垂直像素分析"),
                (self.pixel_horizontal, "水平像素分析")
            ]

            for i, (img_path, name) in enumerate(pixel_images):
                y_pos = pixel_start_y + i * (self.pixel_height + gap)
                slide.Shapes.AddPicture(
                    FileName=str(img_path),
                    LinkToFile=False,
                    SaveWithDocument=True,
                    Left=pixel_start_x,
                    Top=y_pos,
                    Width=self.pixel_width,
                    Height=self.pixel_height
                )
                print(f"✅ {name}图表已添加 (位置: {pixel_start_x:.0f}, {y_pos:.0f})")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加图表失败: {e}")
            return False
    
    def get_info(self):
        """获取PPT信息"""
        if not self.presentation:
            print("❌ 未连接到演示文稿")
            return
        
        print("📊 PPT信息:")
        print(f"   - 文件名: {self.presentation.Name}")
        print(f"   - 幻灯片数量: {self.presentation.Slides.Count}")
        
        ppt_size = self.measure_ppt_size()
        if ppt_size:
            print(f"   - 尺寸: {ppt_size['width_points']:.0f} × {ppt_size['height_points']:.0f} 点")
            print(f"   - 尺寸: {ppt_size['width_cm']:.2f} × {ppt_size['height_cm']:.2f} cm")
        
        if self.presentation.Slides.Count > 0:
            slide = self.presentation.Slides(1)
            print(f"   - 第1张幻灯片形状数: {slide.Shapes.Count}")
    
    def save(self):
        """保存PPT"""
        try:
            if self.presentation:
                self.presentation.Save()
                print("💾 PPT已保存")
                return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
        return False

def main():
    """主函数 - 独立图表排列"""
    print("🎯 独立图表PPT控制器 - yiwu数据集")
    print("=" * 50)
    print("📊 数据集: yiwu")
    print("📊 排列方式: 底部排列")
    print("📊 左侧: 3个IEEE图（PSNR、SSIM、LPIPS）水平并列")
    print("📊 右侧: 2个像素图（垂直、水平）垂直排列")
    print("📊 位置: 都在页面最底端")
    print("⭐ 特色: PSNR图表使用10-40范围优化")
    print("⭐ 特色: 像素分布图使用动态Y轴范围[0.18, 0.53]")
    print("=" * 50)
    
    controller = IndividualChartsPPTController()
    
    # 连接
    if not controller.connect():
        return
    
    # 显示信息
    controller.get_info()
    
    print("\n🎨 开始操作...")

    # 清空第1张幻灯片（替换peitai为bone数据集）
    controller.clear_slide(1)

    # 添加独立图表到第1张幻灯片
    controller.add_individual_charts(1)
    
    # 保存
    controller.save()
    
    print("\n✅ yiwu数据集图表排列完成！")
    print("📊 布局:")
    print("   - 数据集: yiwu")
    print("   - 左侧: IEEE PSNR、SSIM、LPIPS（水平并列）")
    print("   - 右侧: 像素垂直、水平分析（垂直排列）")
    print("   - 位置: 都在页面底部")
    print("   - 特色: PSNR图表Y轴范围10-40优化")
    print("   - 特色: 像素分布图动态Y轴范围[0.18, 0.53]")
    print("   - 文件: ppt4.pptx")

if __name__ == "__main__":
    main()
