#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成符合科研标准的统一规格图表
- IEEE风格图表（宽版）
- 像素分布图（窄版）
- 统一字体大小和高度
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import matplotlib.patches as patches
import cv2
from PIL import Image

class UnifiedChartGenerator:
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.reason_path = self.base_path / "reason"
        self.output_path = Path("E:/reasoning/ppt_demo")
        self.output_path.mkdir(exist_ok=True)
        
        # 统一的图表规格
        self.chart_height = 4.0  # 统一高度（英寸）
        self.ieee_width = 8.0    # IEEE图表宽度（英寸）
        self.pixel_width = 5.0   # 像素分布图宽度（英寸）
        self.font_size = 12      # 统一字体大小
        self.dpi = 300          # 高质量输出
        
        # 统一的样式设置
        plt.rcParams.update({
            'font.size': self.font_size,
            'font.family': 'Arial',
            'axes.labelsize': self.font_size,
            'axes.titlesize': self.font_size + 2,
            'xtick.labelsize': self.font_size - 1,
            'ytick.labelsize': self.font_size - 1,
            'legend.fontsize': self.font_size - 1,
            'figure.titlesize': self.font_size + 4
        })
        
        # 模型配置
        self.models = ["Cycle_GAN", "unet", "FADformer", "MoCE-IR", "Moce-GAN"]
        self.model_display_names = {
            "Cycle_GAN": "CycleGAN",
            "unet": "U-Net",
            "FADformer": "FADformer",
            "MoCE-IR": "MoCE-IR",
            "Moce-GAN": "MoCE-GAN"
        }

        # 像素分布图需要包含GT
        self.pixel_models = ["GT"] + list(self.models)
        self.pixel_display_names = {
            "GT": "GT",
            **self.model_display_names
        }

        # 颜色配置（GT用黑色，其他模型用不同颜色）
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        self.pixel_colors = ['#000000'] + self.colors  # GT用黑色
    
    def load_data(self, dataset_type: str):
        """加载数据"""
        all_data = []

        for model in self.models:
            # 查找模型目录下的CSV文件
            model_dir = self.reason_path / model
            if model_dir.exists():
                # 查找匹配的子目录
                pattern = f"{dataset_type}_data16_512_inference_*"
                matching_dirs = list(model_dir.glob(pattern))

                if matching_dirs:
                    # 使用第一个匹配的目录
                    inference_dir = matching_dirs[0]
                    csv_files = list(inference_dir.glob("inference_logs/*.csv"))

                    if csv_files:
                        csv_path = csv_files[0]  # 使用第一个CSV文件
                        df = pd.read_csv(csv_path)
                        df['Model'] = self.model_display_names[model]
                        all_data.append(df)
                        print(f"✅ 加载 {model}: {len(df)} 条记录 from {csv_path.name}")
                    else:
                        print(f"⚠️ 未找到CSV文件: {inference_dir}")
                else:
                    print(f"⚠️ 未找到匹配目录: {model_dir}/{pattern}")
            else:
                print(f"⚠️ 模型目录不存在: {model_dir}")

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"📊 总计加载: {len(combined_df)} 条记录")
            return combined_df
        return None
    
    def generate_ieee_chart(self, dataset_type: str = "peitai"):
        """生成IEEE风格图表（宽版）"""
        print(f"📊 生成IEEE风格图表 - {dataset_type}数据集")
        
        df = self.load_data(dataset_type)
        if df is None:
            return False
        
        # 创建图表
        fig, axes = plt.subplots(1, 3, figsize=(self.ieee_width, self.chart_height))
        
        metrics = ['PSNR', 'SSIM', 'LPIPS']
        metric_labels = ['PSNR (dB)', 'SSIM', 'LPIPS']
        
        for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
            ax = axes[i]
            
            # 创建箱线图
            box_data = [df[df['Model'] == model][metric].values 
                       for model in [self.model_display_names[m] for m in self.models]]
            
            bp = ax.boxplot(box_data, labels=[self.model_display_names[m] for m in self.models],
                           patch_artist=True, showfliers=False)
            
            # 设置颜色
            for patch, color in zip(bp['boxes'], self.colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            # 设置样式
            ax.set_title(f'{metric}', fontweight='bold', fontsize=self.font_size + 1)
            ax.set_ylabel(label, fontsize=self.font_size)
            ax.tick_params(axis='x', rotation=45, labelsize=self.font_size - 1)
            ax.tick_params(axis='y', labelsize=self.font_size - 1)
            ax.grid(True, alpha=0.3)
            
            # 设置Y轴范围
            if metric == 'PSNR':
                ax.set_ylim(20, 40)
            elif metric == 'SSIM':
                ax.set_ylim(0.6, 1.0)
            elif metric == 'LPIPS':
                ax.set_ylim(0, 0.4)
        
        plt.tight_layout()
        
        # 保存
        output_file = self.output_path / f"ieee_chart_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"✅ IEEE图表已保存: {output_file}")
        return True
    
    def extract_pixel_line(self, image_path: Path, direction='vertical'):
        """从图像中提取垂直或水平线的像素强度"""
        try:
            from PIL import Image
            import cv2

            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"⚠️ 无法读取图像: {image_path}")
                return None

            # 转换为灰度
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape

            if direction == 'vertical':
                # 提取中心垂直线
                center_x = w // 2
                line_pixels = gray[:, center_x]
            else:  # horizontal
                # 提取中心水平线
                center_y = h // 2
                line_pixels = gray[center_y, :]

            # 归一化到0-1
            normalized = line_pixels.astype(np.float32) / 255.0
            return normalized

        except Exception as e:
            print(f"❌ 提取像素线失败 {image_path}: {e}")
            return None

    def load_pixel_data(self, dataset_type: str):
        """加载真实的像素分布数据"""
        print(f"📊 加载真实像素数据 - {dataset_type}数据集")

        # 数据路径
        filter_path = self.base_path / "visual" / "filter"

        pixel_data = {}

        # 加载GT数据
        gt_files = {
            'peitai': {
                'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_GT_region1_enlarged.png",
                'region2': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_GT_region2_enlarged.png"
            },
            'bone': {
                'region1': filter_path / "Cycle_GAN" / "bone" / "local_comparison" / "16_512" / "2292_PSNR22.81_GT_region1_enlarged.png",
                'region2': filter_path / "Cycle_GAN" / "bone" / "local_comparison" / "16_512" / "2292_PSNR22.81_GT_region2_enlarged.png"
            }
        }

        # 模型文件映射
        model_files = {
            'peitai': {
                'Cycle_GAN': {
                    'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_Restored_region1_enlarged.png",
                    'region2': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_Restored_region2_enlarged.png"
                },
                'unet': {
                    'region1': filter_path / "unet" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR28.28_Restored_region1_enlarged.png",
                    'region2': filter_path / "unet" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR28.28_Restored_region2_enlarged.png"
                },
                'FADformer': {
                    'region1': filter_path / "FADformer" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR31.12_Restored_region1_enlarged.png",
                    'region2': filter_path / "FADformer" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR31.12_Restored_region2_enlarged.png"
                },
                'MoCE-IR': {
                    'region1': filter_path / "MoCE-IR" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.88_Restored_region1_enlarged.png",
                    'region2': filter_path / "MoCE-IR" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.88_Restored_region2_enlarged.png"
                },
                'Moce-GAN': {
                    'region1': filter_path / "Moce-GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR38.30_Restored_region1_enlarged.png",
                    'region2': filter_path / "Moce-GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR38.30_Restored_region2_enlarged.png"
                }
            }
        }

        # 提取GT像素数据
        gt_region1_v = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'vertical')
        gt_region1_h = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'horizontal')

        if gt_region1_v is not None and gt_region1_h is not None:
            pixel_data['GT'] = {
                'vertical': gt_region1_v,
                'horizontal': gt_region1_h
            }
            print(f"✅ 加载GT像素数据: {len(gt_region1_v)} 像素点")

        # 提取各模型像素数据
        for model in self.models:
            if dataset_type in model_files and model in model_files[dataset_type]:
                region1_path = model_files[dataset_type][model]['region1']

                if region1_path.exists():
                    v_pixels = self.extract_pixel_line(region1_path, 'vertical')
                    h_pixels = self.extract_pixel_line(region1_path, 'horizontal')

                    if v_pixels is not None and h_pixels is not None:
                        pixel_data[model] = {
                            'vertical': v_pixels,
                            'horizontal': h_pixels
                        }
                        print(f"✅ 加载{self.model_display_names[model]}像素数据: {len(v_pixels)} 像素点")
                else:
                    print(f"⚠️ 文件不存在: {region1_path}")

        return pixel_data

    def generate_pixel_distribution(self, dataset_type: str = "peitai"):
        """生成真实像素分布图（窄版）"""
        print(f"📈 生成真实像素分布图 - {dataset_type}数据集")

        # 加载真实像素数据
        pixel_data = self.load_pixel_data(dataset_type)

        if not pixel_data:
            print("❌ 无法加载像素数据")
            return False

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.pixel_width, self.chart_height))

        # 绘制垂直线分析
        color_idx = 0
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                display_name = self.pixel_display_names.get(model_key, model_key)
                v_data = pixel_data[model_key]['vertical']

                x_positions = np.arange(len(v_data))
                linewidth = 2.5 if model_key == 'GT' else 2

                ax1.plot(x_positions, v_data, label=display_name,
                        color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)
                color_idx += 1

        ax1.set_ylabel('Pixel Value', fontweight='bold', fontsize=self.font_size)
        ax1.set_xlabel('Position (pixels)', fontsize=self.font_size)
        ax1.legend(fontsize=self.font_size - 2, loc='upper right')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)

        # 绘制水平线分析
        color_idx = 0
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                display_name = self.pixel_display_names.get(model_key, model_key)
                h_data = pixel_data[model_key]['horizontal']

                x_positions = np.arange(len(h_data))
                linewidth = 2.5 if model_key == 'GT' else 2

                ax2.plot(x_positions, h_data, label=display_name,
                        color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)
                color_idx += 1

        ax2.set_ylabel('Pixel Value', fontweight='bold', fontsize=self.font_size)
        ax2.set_xlabel('Position (pixels)', fontsize=self.font_size)
        ax2.legend(fontsize=self.font_size - 2, loc='upper right')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)

        # 调整布局，减少整体边距
        plt.tight_layout(pad=1.0)

        # 保存
        output_file = self.output_path / f"pixel_distribution_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print(f"✅ 真实像素分布图已保存: {output_file}")
        return True
    
    def generate_all_charts(self, dataset_type: str = "peitai"):
        """生成所有图表"""
        print("🎨 生成统一规格的科研图表")
        print("=" * 50)
        print(f"📊 配置信息:")
        print(f"   - 数据集: {dataset_type}")
        print(f"   - IEEE图表尺寸: {self.ieee_width}\" × {self.chart_height}\"")
        print(f"   - 像素分布图尺寸: {self.pixel_width}\" × {self.chart_height}\"")
        print(f"   - 统一字体大小: {self.font_size}pt")
        print(f"   - 输出DPI: {self.dpi}")
        print("=" * 50)
        
        success1 = self.generate_ieee_chart(dataset_type)
        success2 = self.generate_pixel_distribution(dataset_type)
        
        if success1 and success2:
            print("\n🎉 所有图表生成完成！")
            print(f"📁 输出目录: {self.output_path}")
            return True
        else:
            print("\n❌ 图表生成失败！")
            return False

def main():
    """主函数"""
    generator = UnifiedChartGenerator()
    generator.generate_all_charts("peitai")

if __name__ == "__main__":
    main()
