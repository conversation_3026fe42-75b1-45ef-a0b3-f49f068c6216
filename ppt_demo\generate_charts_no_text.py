#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成无文字版本的图表 - 用于原尺寸插入PPT
不包含任何文字标签，用户后续在PPT中自行添加
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import matplotlib.patches as patches
import cv2
from PIL import Image

class NoTextChartGenerator:
    def __init__(self, base_path: str = r"E:\reasoning"):
        self.base_path = Path(base_path)
        self.reason_path = self.base_path / "reason"
        self.output_path = Path("E:/reasoning/ppt_demo")
        self.output_path.mkdir(exist_ok=True)
        
        # 图表规格 - 根据PPT宽度17cm计算
        # PPT宽度17cm = 481.89点，留边距后可用宽度约450点
        # IEEE图表占60%，像素分布图占35%，间距5%
        self.ieee_width_points = 270  # 约60%
        self.pixel_width_points = 175  # 约35%
        self.chart_height_points = 120  # 统一高度
        
        # 转换为英寸 (72点 = 1英寸)
        self.ieee_width = self.ieee_width_points / 72
        self.pixel_width = self.pixel_width_points / 72
        self.chart_height = self.chart_height_points / 72
        
        self.dpi = 300  # 高质量输出
        
        # 完全关闭所有文字
        plt.rcParams.update({
            'font.size': 0,
            'axes.labelsize': 0,
            'axes.titlesize': 0,
            'xtick.labelsize': 0,
            'ytick.labelsize': 0,
            'legend.fontsize': 0,
            'figure.titlesize': 0,
            'xtick.major.size': 0,
            'xtick.minor.size': 0,
            'ytick.major.size': 0,
            'ytick.minor.size': 0
        })
        
        # 模型配置
        self.models = ["Cycle_GAN", "unet", "FADformer", "MoCE-IR", "Moce-GAN"]
        self.model_display_names = {
            "Cycle_GAN": "CycleGAN",
            "unet": "U-Net", 
            "FADformer": "FADformer",
            "MoCE-IR": "MoCE-IR",
            "Moce-GAN": "MoCE-GAN"
        }
        
        # 像素分布图需要包含GT
        self.pixel_models = ["GT"] + list(self.models)
        self.pixel_display_names = {
            "GT": "GT",
            **self.model_display_names
        }
        
        # 颜色配置
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        self.pixel_colors = ['#000000'] + self.colors  # GT用黑色
    
    def load_data(self, dataset_type: str):
        """加载数据"""
        all_data = []
        
        for model in self.models:
            # 查找模型目录下的CSV文件
            model_dir = self.reason_path / model
            if model_dir.exists():
                # 查找匹配的子目录
                pattern = f"{dataset_type}_data16_512_inference_*"
                matching_dirs = list(model_dir.glob(pattern))
                
                if matching_dirs:
                    # 使用第一个匹配的目录
                    inference_dir = matching_dirs[0]
                    csv_files = list(inference_dir.glob("inference_logs/*.csv"))
                    
                    if csv_files:
                        csv_path = csv_files[0]  # 使用第一个CSV文件
                        df = pd.read_csv(csv_path)
                        df['Model'] = self.model_display_names[model]
                        all_data.append(df)
                        print(f"✅ 加载 {model}: {len(df)} 条记录")
                    else:
                        print(f"⚠️ 未找到CSV文件: {inference_dir}")
                else:
                    print(f"⚠️ 未找到匹配目录: {model_dir}/{pattern}")
            else:
                print(f"⚠️ 模型目录不存在: {model_dir}")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"📊 总计加载: {len(combined_df)} 条记录")
            return combined_df
        return None
    
    def generate_ieee_chart_no_text(self, dataset_type: str = "peitai"):
        """生成无文字的IEEE风格图表"""
        print(f"📊 生成无文字IEEE风格图表 - {dataset_type}数据集")
        
        df = self.load_data(dataset_type)
        if df is None:
            return False
        
        # 创建图表 - 无任何标题
        fig, axes = plt.subplots(1, 3, figsize=(self.ieee_width, self.chart_height))
        
        metrics = ['PSNR', 'SSIM', 'LPIPS']
        
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            # 创建箱线图
            box_data = [df[df['Model'] == model][metric].values 
                       for model in [self.model_display_names[m] for m in self.models]]
            
            bp = ax.boxplot(box_data, patch_artist=True, showfliers=False)
            
            # 设置颜色
            for patch, color in zip(bp['boxes'], self.colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            # 移除所有文字和标签
            ax.set_xticklabels([])
            ax.set_yticklabels([])
            ax.set_xlabel('')
            ax.set_ylabel('')
            ax.set_title('')

            # 保留网格但移除刻度
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelbottom=False, labelleft=False)
            
            # 设置Y轴范围
            if metric == 'PSNR':
                ax.set_ylim(20, 40)
            elif metric == 'SSIM':
                ax.set_ylim(0.6, 1.0)
            elif metric == 'LPIPS':
                ax.set_ylim(0, 0.4)
        
        # 移除子图间的间距
        plt.subplots_adjust(left=0, right=1, top=1, bottom=0, wspace=0.1)
        
        # 保存
        output_file = self.output_path / f"ieee_chart_no_text_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none', pad_inches=0)
        plt.close()
        
        print(f"✅ 无文字IEEE图表已保存: {output_file}")
        return True
    
    def extract_pixel_line(self, image_path: Path, direction='vertical'):
        """从图像中提取垂直或水平线的像素强度"""
        try:
            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"⚠️ 无法读取图像: {image_path}")
                return None
            
            # 转换为灰度
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape
            
            if direction == 'vertical':
                # 提取中心垂直线
                center_x = w // 2
                line_pixels = gray[:, center_x]
            else:  # horizontal
                # 提取中心水平线
                center_y = h // 2
                line_pixels = gray[center_y, :]
            
            # 归一化到0-1
            normalized = line_pixels.astype(np.float32) / 255.0
            return normalized
            
        except Exception as e:
            print(f"❌ 提取像素线失败 {image_path}: {e}")
            return None
    
    def load_pixel_data(self, dataset_type: str):
        """加载真实的像素分布数据"""
        print(f"📊 加载真实像素数据 - {dataset_type}数据集")
        
        # 数据路径
        filter_path = self.base_path / "visual" / "filter"
        
        pixel_data = {}
        
        # GT文件路径
        gt_files = {
            'peitai': {
                'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_GT_region1_enlarged.png",
            }
        }
        
        # 模型文件映射
        model_files = {
            'peitai': {
                'Cycle_GAN': {
                    'region1': filter_path / "Cycle_GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.63_Restored_region1_enlarged.png",
                },
                'unet': {
                    'region1': filter_path / "unet" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR28.28_Restored_region1_enlarged.png",
                },
                'FADformer': {
                    'region1': filter_path / "FADformer" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR31.12_Restored_region1_enlarged.png",
                },
                'MoCE-IR': {
                    'region1': filter_path / "MoCE-IR" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR26.88_Restored_region1_enlarged.png",
                },
                'Moce-GAN': {
                    'region1': filter_path / "Moce-GAN" / "peitai" / "local_comparison" / "16_512" / "1467_PSNR38.30_Restored_region1_enlarged.png",
                }
            }
        }
        
        # 提取GT像素数据
        gt_region1_v = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'vertical')
        gt_region1_h = self.extract_pixel_line(gt_files[dataset_type]['region1'], 'horizontal')
        
        if gt_region1_v is not None and gt_region1_h is not None:
            pixel_data['GT'] = {
                'vertical': gt_region1_v,
                'horizontal': gt_region1_h
            }
            print(f"✅ 加载GT像素数据: {len(gt_region1_v)} 像素点")
        
        # 提取各模型像素数据
        for model in self.models:
            if dataset_type in model_files and model in model_files[dataset_type]:
                region1_path = model_files[dataset_type][model]['region1']
                
                if region1_path.exists():
                    v_pixels = self.extract_pixel_line(region1_path, 'vertical')
                    h_pixels = self.extract_pixel_line(region1_path, 'horizontal')
                    
                    if v_pixels is not None and h_pixels is not None:
                        pixel_data[model] = {
                            'vertical': v_pixels,
                            'horizontal': h_pixels
                        }
                        print(f"✅ 加载{self.model_display_names[model]}像素数据: {len(v_pixels)} 像素点")
                else:
                    print(f"⚠️ 文件不存在: {region1_path}")
        
        return pixel_data
    
    def generate_pixel_distribution_no_text(self, dataset_type: str = "peitai"):
        """生成无文字的像素分布图"""
        print(f"📈 生成无文字像素分布图 - {dataset_type}数据集")
        
        # 加载真实像素数据
        pixel_data = self.load_pixel_data(dataset_type)
        
        if not pixel_data:
            print("❌ 无法加载像素数据")
            return False
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.pixel_width, self.chart_height))
        
        # 绘制垂直线分析
        color_idx = 0
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                v_data = pixel_data[model_key]['vertical']
                
                x_positions = np.arange(len(v_data))
                linewidth = 2.5 if model_key == 'GT' else 2
                
                ax1.plot(x_positions, v_data,
                        color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)
                color_idx += 1
        
        # 移除所有文字和标签
        ax1.set_xticklabels([])
        ax1.set_yticklabels([])
        ax1.set_xlabel('')
        ax1.set_ylabel('')
        ax1.set_title('')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(labelbottom=False, labelleft=False)
        ax1.set_ylim(0, 1)

        # 绘制水平线分析
        color_idx = 0
        for model_key in ['GT'] + self.models:
            if model_key in pixel_data:
                h_data = pixel_data[model_key]['horizontal']

                x_positions = np.arange(len(h_data))
                linewidth = 2.5 if model_key == 'GT' else 2

                ax2.plot(x_positions, h_data,
                        color=self.pixel_colors[color_idx], linewidth=linewidth, alpha=0.9)
                color_idx += 1

        # 移除所有文字和标签
        ax2.set_xticklabels([])
        ax2.set_yticklabels([])
        ax2.set_xlabel('')
        ax2.set_ylabel('')
        ax2.set_title('')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(labelbottom=False, labelleft=False)
        ax2.set_ylim(0, 1)

        # 移除子图间的间距
        plt.subplots_adjust(left=0, right=1, top=1, bottom=0, hspace=0.1)
        
        # 保存
        output_file = self.output_path / f"pixel_distribution_no_text_{dataset_type}.png"
        plt.savefig(output_file, dpi=self.dpi, bbox_inches='tight',
                   facecolor='white', edgecolor='none', pad_inches=0)
        plt.close()
        
        print(f"✅ 无文字像素分布图已保存: {output_file}")
        return True
    
    def generate_all_charts(self, dataset_type: str = "peitai"):
        """生成所有无文字图表"""
        print("🎨 生成无文字版本的科研图表")
        print("=" * 50)
        print(f"📊 配置信息:")
        print(f"   - 数据集: {dataset_type}")
        print(f"   - IEEE图表尺寸: {self.ieee_width:.2f}\" × {self.chart_height:.2f}\" ({self.ieee_width_points}×{self.chart_height_points}点)")
        print(f"   - 像素分布图尺寸: {self.pixel_width:.2f}\" × {self.chart_height:.2f}\" ({self.pixel_width_points}×{self.chart_height_points}点)")
        print(f"   - 无任何文字标签")
        print(f"   - 输出DPI: {self.dpi}")
        print("=" * 50)
        
        success1 = self.generate_ieee_chart_no_text(dataset_type)
        success2 = self.generate_pixel_distribution_no_text(dataset_type)
        
        if success1 and success2:
            print("\n🎉 所有无文字图表生成完成！")
            print(f"📁 输出目录: {self.output_path}")
            return True
        else:
            print("\n❌ 图表生成失败！")
            return False

def main():
    """主函数"""
    generator = NoTextChartGenerator()
    generator.generate_all_charts("peitai")

if __name__ == "__main__":
    main()
