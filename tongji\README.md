# 模型性能统计表格说明

## 文件说明

本目录包含三个数据集的模型性能统计表格：

- `bone_performance_table.csv` - Bone数据集性能表
- `peitai_performance_table.csv` - Peitai数据集性能表  
- `yiwu_performance_table.csv` - Yiwu数据集性能表

## 表格格式

每个CSV文件包含5个模型在不同数据配置下的性能指标：

### 数据配置
- **仿真数据集** (Bone & Peitai): 16-512, 32-512, 64-512, 128-512
- **实验数据集** (Yiwu): 16-256, 32-256, 64-256, 128-256

### 评估指标
- **PSNR**: 峰值信噪比 (越高越好)
- **SSIM**: 结构相似性指数 (越高越好)  
- **LPIPS**: 感知图像补丁相似性 (越低越好)

## 三线表绘制要求

### 表格结构要求：
1. **第一行**：指标名称 - PSNR | SSIM | LPIPS
2. **模型排序**（从上到下）：
   - CycleGAN
   - U-Net
   - FADformer
   - MoCE-IR
   - **MoCE-GAN** (我的模型，放在最后)

### 表格样式要求：
- 使用标准的三线表格式（顶线、中线、底线）
- PSNR保留2位小数
- SSIM和LPIPS保留4位小数
- 突出显示最佳性能值（加粗或标记）
- 我的模型（MoCE-GAN）行可以特殊标记

### 数据列顺序：
按数据量从小到大排列：
- **仿真数据集**: 16-512 → 32-512 → 64-512 → 128-512
- **实验数据集**: 16-256 → 32-256 → 64-256 → 128-256

## 示例表格结构

```
模型      | 16-512        | 32-512        | 64-512        | ...
         | PSNR|SSIM|LPIPS| PSNR|SSIM|LPIPS| PSNR|SSIM|LPIPS|
---------|---------------|---------------|---------------|
CycleGAN | xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx|
U-Net    | xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx|
FADformer| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx|
MoCE-IR  | xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx|
MoCE-GAN | xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx| xx.xx|x.xxxx|x.xxxx|
```

## 注意事项

1. **MoCE-GAN是我的模型**，请在表格中特殊标记或放在最后一行
2. 每个数据配置下都有三个指标值
3. 可以考虑用颜色或加粗标记每列的最佳性能值
4. 表格标题建议包含数据集名称，如"Bone数据集模型性能对比"

## 生成时间
2025-07-24

## 联系信息
如有疑问，请参考CSV文件中的具体数据格式。
