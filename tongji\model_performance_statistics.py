#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能统计分析系统
统计不同模型在不同数据集上的平均PSNR、SSIM、LPIPS值
用于生成三线表数据

作者: Augment Agent
日期: 2025-07-24
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
import re
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class ModelPerformanceAnalyzer:
    def __init__(self, reason_path: str, output_path: str):
        """
        初始化分析器
        
        Args:
            reason_path: 推理结果根目录路径
            output_path: 输出目录路径
        """
        self.reason_path = Path(reason_path)
        self.output_path = Path(output_path)
        self.output_path.mkdir(exist_ok=True)
        
        # 模型名称映射
        self.model_mapping = {
            'Cycle_GAN': 'CycleGAN',
            'FADformer': 'FADformer', 
            'Moce-GAN': 'MoCE-GAN',
            'MoCE-IR': 'MoCE-IR',
            'unet': 'U-Net'
        }
        
        # 数据集名称映射
        self.dataset_mapping = {
            'yiwu': 'Yiwu',
            'peitai': 'Peitai', 
            'bone': 'Bone'
        }
        
        print("🚀 初始化模型性能统计分析系统...")
        print(f"📁 推理结果目录: {self.reason_path}")
        print(f"📁 输出目录: {self.output_path}")

    def parse_experiment_info(self, folder_name: str) -> Tuple[str, str, int]:
        """
        解析实验文件夹名称，提取数据集、数据量、图像尺寸信息
        
        Args:
            folder_name: 文件夹名称，如 "yiwu_data16_256_inference_2025_07_23_00_50_42"
            
        Returns:
            (dataset, data_size, image_size): 数据集名称、数据量、图像尺寸
        """
        # 匹配模式：dataset_data{size}_{image_size}_inference_...
        pattern = r'(\w+)_data(\d+)_(\d+)_inference'
        match = re.match(pattern, folder_name)
        
        if match:
            dataset = match.group(1)
            data_size = int(match.group(2))
            image_size = int(match.group(3))
            return dataset, data_size, image_size
        else:
            raise ValueError(f"无法解析文件夹名称: {folder_name}")

    def load_metrics_from_csv(self, csv_path: Path) -> pd.DataFrame:
        """
        从CSV文件加载指标数据
        
        Args:
            csv_path: CSV文件路径
            
        Returns:
            包含PSNR、SSIM、LPIPS的DataFrame
        """
        try:
            df = pd.read_csv(csv_path, encoding='utf-8')
            # 确保列名正确
            if '图片名' in df.columns:
                df = df.rename(columns={'图片名': 'Image'})
            return df[['PSNR', 'SSIM', 'LPIPS']]
        except Exception as e:
            print(f"❌ 读取CSV文件失败: {csv_path}, 错误: {e}")
            return pd.DataFrame()

    def scan_all_experiments(self) -> List[Dict]:
        """
        扫描所有实验结果
        
        Returns:
            实验信息列表
        """
        experiments = []
        
        print("🔍 扫描所有实验结果...")
        
        for model_dir in self.reason_path.iterdir():
            if not model_dir.is_dir():
                continue
                
            model_name = model_dir.name
            if model_name not in self.model_mapping:
                print(f"⚠️  跳过未知模型: {model_name}")
                continue
                
            print(f"📊 处理模型: {self.model_mapping[model_name]}")
            
            for exp_dir in model_dir.iterdir():
                if not exp_dir.is_dir():
                    continue
                    
                try:
                    # 解析实验信息
                    dataset, data_size, image_size = self.parse_experiment_info(exp_dir.name)
                    
                    # 查找CSV文件
                    logs_dir = exp_dir / "inference_logs"
                    if not logs_dir.exists():
                        print(f"⚠️  未找到logs目录: {logs_dir}")
                        continue
                        
                    csv_files = list(logs_dir.glob("*.csv"))
                    if not csv_files:
                        print(f"⚠️  未找到CSV文件: {logs_dir}")
                        continue
                        
                    csv_path = csv_files[0]  # 取第一个CSV文件
                    
                    # 加载指标数据
                    metrics_df = self.load_metrics_from_csv(csv_path)
                    if metrics_df.empty:
                        continue
                        
                    # 计算平均值
                    avg_metrics = {
                        'PSNR': metrics_df['PSNR'].mean(),
                        'SSIM': metrics_df['SSIM'].mean(), 
                        'LPIPS': metrics_df['LPIPS'].mean()
                    }
                    
                    experiment_info = {
                        'Model': self.model_mapping[model_name],
                        'Dataset': self.dataset_mapping.get(dataset, dataset.capitalize()),
                        'Data_Size': data_size,
                        'Image_Size': image_size,
                        'Sample_Count': len(metrics_df),
                        **avg_metrics,
                        'CSV_Path': str(csv_path)
                    }
                    
                    experiments.append(experiment_info)
                    print(f"  ✅ {dataset}_data{data_size}_{image_size}: "
                          f"PSNR={avg_metrics['PSNR']:.2f}, "
                          f"SSIM={avg_metrics['SSIM']:.4f}, "
                          f"LPIPS={avg_metrics['LPIPS']:.4f}")
                    
                except Exception as e:
                    print(f"❌ 处理实验失败: {exp_dir.name}, 错误: {e}")
                    continue
        
        print(f"📈 总共处理了 {len(experiments)} 个实验")
        return experiments

    def generate_summary_statistics(self, experiments: List[Dict]) -> pd.DataFrame:
        """
        生成汇总统计表
        
        Args:
            experiments: 实验结果列表
            
        Returns:
            汇总统计DataFrame
        """
        print("📊 生成汇总统计表...")
        
        # 转换为DataFrame
        df = pd.DataFrame(experiments)
        
        # 按模型和数据集分组，计算平均值
        summary = df.groupby(['Model', 'Dataset']).agg({
            'PSNR': ['mean', 'std', 'count'],
            'SSIM': ['mean', 'std', 'count'], 
            'LPIPS': ['mean', 'std', 'count'],
            'Sample_Count': 'sum'
        }).round(4)
        
        # 展平多级列名
        summary.columns = [f'{col[0]}_{col[1]}' for col in summary.columns]
        summary = summary.reset_index()
        
        return summary

    def generate_three_line_table_data(self, experiments: List[Dict]) -> pd.DataFrame:
        """
        生成适合三线表的数据格式（综合表）

        Args:
            experiments: 实验结果列表

        Returns:
            三线表格式的DataFrame
        """
        print("📋 生成综合三线表数据...")

        df = pd.DataFrame(experiments)

        # 按模型和数据集分组，只保留平均值
        pivot_data = []

        for model in sorted(df['Model'].unique()):
            model_data = df[df['Model'] == model]

            row = {'Model': model}

            # 为每个数据集添加指标
            for dataset in sorted(df['Dataset'].unique()):
                dataset_data = model_data[model_data['Dataset'] == dataset]

                if not dataset_data.empty:
                    # 计算该模型在该数据集上的平均值
                    avg_psnr = dataset_data['PSNR'].mean()
                    avg_ssim = dataset_data['SSIM'].mean()
                    avg_lpips = dataset_data['LPIPS'].mean()

                    row[f'{dataset}_PSNR'] = round(avg_psnr, 2)
                    row[f'{dataset}_SSIM'] = round(avg_ssim, 4)
                    row[f'{dataset}_LPIPS'] = round(avg_lpips, 4)
                else:
                    row[f'{dataset}_PSNR'] = None
                    row[f'{dataset}_SSIM'] = None
                    row[f'{dataset}_LPIPS'] = None

            pivot_data.append(row)

        return pd.DataFrame(pivot_data)

    def generate_dataset_specific_tables(self, experiments: List[Dict]) -> Dict[str, pd.DataFrame]:
        """
        生成针对每个数据集的综合表格：包含PSNR、SSIM、LPIPS三个指标

        Args:
            experiments: 实验结果列表

        Returns:
            包含每个数据集表格的字典
        """
        print("📋 生成分数据集综合表格数据...")

        df = pd.DataFrame(experiments)
        dataset_tables = {}

        # 为每个数据集生成独立的表格
        for dataset in sorted(df['Dataset'].unique()):
            print(f"  📊 处理{dataset}数据集...")

            dataset_data = df[df['Dataset'] == dataset]

            # 创建数据量配置标识
            dataset_data = dataset_data.copy()
            dataset_data['Config'] = dataset_data['Data_Size'].astype(str) + '-' + dataset_data['Image_Size'].astype(str)

            # 获取所有配置和模型
            configs = sorted(dataset_data['Config'].unique())
            models = sorted(dataset_data['Model'].unique())

            # 创建综合表格
            table_data = []

            for model in models:
                model_data = dataset_data[dataset_data['Model'] == model]

                row = {'Model': model}

                # 为每个配置添加三个指标
                for config in configs:
                    config_data = model_data[model_data['Config'] == config]

                    if not config_data.empty:
                        psnr = round(config_data['PSNR'].mean(), 2)
                        ssim = round(config_data['SSIM'].mean(), 4)
                        lpips = round(config_data['LPIPS'].mean(), 4)

                        row[f'{config}_PSNR'] = psnr
                        row[f'{config}_SSIM'] = ssim
                        row[f'{config}_LPIPS'] = lpips
                    else:
                        row[f'{config}_PSNR'] = None
                        row[f'{config}_SSIM'] = None
                        row[f'{config}_LPIPS'] = None

                table_data.append(row)

            dataset_tables[dataset] = pd.DataFrame(table_data)

        return dataset_tables

    def save_results(self, experiments: List[Dict], summary_df: pd.DataFrame,
                    three_line_df: pd.DataFrame, dataset_tables: Dict[str, pd.DataFrame]):
        """
        保存所有结果

        Args:
            experiments: 原始实验数据
            summary_df: 汇总统计数据
            three_line_df: 综合三线表数据
            dataset_tables: 分数据集表格字典
        """
        print("💾 保存分析结果...")

        # 保存原始实验数据
        raw_df = pd.DataFrame(experiments)
        raw_df.to_csv(self.output_path / "raw_experiment_data.csv",
                     index=False, encoding='utf-8-sig')
        print(f"✅ 保存原始数据: {self.output_path / 'raw_experiment_data.csv'}")

        # 保存汇总统计
        summary_df.to_csv(self.output_path / "model_performance_summary.csv",
                         index=False, encoding='utf-8-sig')
        print(f"✅ 保存汇总统计: {self.output_path / 'model_performance_summary.csv'}")

        # 保存综合三线表数据
        three_line_df.to_csv(self.output_path / "three_line_table_data.csv",
                            index=False, encoding='utf-8-sig')
        print(f"✅ 保存综合三线表数据: {self.output_path / 'three_line_table_data.csv'}")

        # 保存分数据集的综合表格数据
        for dataset, table_df in dataset_tables.items():
            filename = f"{dataset.lower()}_performance_table.csv"
            filepath = self.output_path / filename
            table_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"✅ 保存{dataset}数据集综合表格: {filepath}")

    def generate_report(self, experiments: List[Dict], summary_df: pd.DataFrame):
        """
        生成分析报告
        
        Args:
            experiments: 实验数据
            summary_df: 汇总统计数据
        """
        print("📋 生成分析报告...")
        
        report_path = self.output_path / "performance_analysis_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 模型性能统计分析报告\n\n")
            f.write(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 分析概述\n\n")
            f.write(f"- **总实验数量**: {len(experiments)}\n")
            f.write(f"- **模型数量**: {len(set(exp['Model'] for exp in experiments))}\n")
            f.write(f"- **数据集数量**: {len(set(exp['Dataset'] for exp in experiments))}\n")
            f.write(f"- **总样本数量**: {sum(exp['Sample_Count'] for exp in experiments)}\n\n")
            
            f.write("## 评估指标\n\n")
            f.write("- **PSNR**: 峰值信噪比，值越高表示重建质量越好\n")
            f.write("- **SSIM**: 结构相似性指数，值越高表示结构保持越好\n")
            f.write("- **LPIPS**: 感知图像补丁相似性，值越低表示感知质量越好\n\n")
            
            f.write("## 模型列表\n\n")
            models = sorted(set(exp['Model'] for exp in experiments))
            for model in models:
                f.write(f"- {model}\n")
            f.write("\n")
            
            f.write("## 数据集列表\n\n")
            datasets = sorted(set(exp['Dataset'] for exp in experiments))
            for dataset in datasets:
                f.write(f"- {dataset}\n")
            f.write("\n")
            
            f.write("## 文件结构\n\n")
            f.write("```\n")
            f.write("tongji/\n")
            f.write("├── model_performance_statistics.py    # 主分析脚本\n")
            f.write("├── raw_experiment_data.csv           # 原始实验数据\n")
            f.write("├── model_performance_summary.csv     # 汇总统计数据\n")
            f.write("├── three_line_table_data.csv         # 综合三线表格式数据\n")
            f.write("├── bone_performance_table.csv        # Bone数据集性能表\n")
            f.write("├── peitai_performance_table.csv      # Peitai数据集性能表\n")
            f.write("├── yiwu_performance_table.csv        # Yiwu数据集性能表\n")
            f.write("└── performance_analysis_report.md    # 分析报告\n")
            f.write("```\n\n")

            f.write("## 使用说明\n\n")
            f.write("1. **raw_experiment_data.csv**: 包含所有实验的详细信息\n")
            f.write("2. **model_performance_summary.csv**: 按模型和数据集分组的统计信息\n")
            f.write("3. **three_line_table_data.csv**: 综合三线表格式数据（所有数据集）\n")
            f.write("4. **{dataset}_performance_table.csv**: 单独数据集的性能表格，包含均值和标准差\n\n")
        
        print(f"✅ 生成分析报告: {report_path}")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("=" * 60)
        print("🚀 开始模型性能统计分析...")
        print("=" * 60)
        
        # 1. 扫描所有实验
        experiments = self.scan_all_experiments()
        
        if not experiments:
            print("❌ 未找到任何实验数据！")
            return
        
        # 2. 生成汇总统计
        summary_df = self.generate_summary_statistics(experiments)

        # 3. 生成综合三线表数据
        three_line_df = self.generate_three_line_table_data(experiments)

        # 4. 生成分数据集表格
        dataset_tables = self.generate_dataset_specific_tables(experiments)

        # 5. 保存结果
        self.save_results(experiments, summary_df, three_line_df, dataset_tables)
        
        # 5. 生成报告
        self.generate_report(experiments, summary_df)
        
        print("=" * 60)
        print("🎉 模型性能统计分析完成！")
        print(f"📁 输出目录: {self.output_path}")
        print("📊 生成的文件:")
        print("   - raw_experiment_data.csv")
        print("   - model_performance_summary.csv")
        print("   - three_line_table_data.csv")
        print("   - bone_performance_table.csv")
        print("   - peitai_performance_table.csv")
        print("   - yiwu_performance_table.csv")
        print("   - performance_analysis_report.md")
        print("=" * 60)

def main():
    """主函数"""
    reason_path = r"E:\reasoning\reason"
    output_path = r"E:\reasoning\tongji"
    
    analyzer = ModelPerformanceAnalyzer(reason_path, output_path)
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
